# sqlite_service.py
"""
Module chịu trách nhiệm cho tất cả các tương tác với CSDL SQLite.
Bao gồm hai lớp chính:
1. ProjectService: Quản lý CSDL trung tâm chứa danh sách và cấu hình các dự án.
2. ProjectDataService: Quản lý CSDL cache riêng cho từng dự án, lưu trữ kết quả
   phân tích file, thông tin CSDL, menu, v.v. để tăng tốc độ tải.
"""
import sqlite3
import os
import json
import re
from typing import Dict, List, Any, Tuple
import time
import pandas as pd
import logging
from collections import defaultdict

from constants import SQLITE_CACHE_DIR, FILE_EXTENSIONS, EXCLUDED_FOLDERS, EXCLUDED_FILE_EXTENSIONS

logger = logging.getLogger(__name__)

# ===================================================================
# LỚP CƠ SỞ
# ===================================================================

class BaseSQLiteService:
    """Lớp cơ sở cung cấp logic quản lý kết nối CSDL SQLite chung."""
    def __init__(self, db_path: str):
        self.db_path = db_path
        db_dir = os.path.dirname(self.db_path)
        if not os.path.exists(db_dir):
            os.makedirs(db_dir)

    def _get_connection(self) -> sqlite3.Connection:
        """Tạo và trả về một kết nối CSDL mới."""
        conn = sqlite3.connect(self.db_path)
        conn.execute("PRAGMA foreign_keys = ON;")
        return conn

# ===================================================================
# SERVICE QUẢN LÝ DỰ ÁN
# ===================================================================

class ProjectService(BaseSQLiteService):
    """Quản lý thông tin các dự án trong CSDL SQLite trung tâm."""
    def __init__(self):
        db_path = os.path.join(SQLITE_CACHE_DIR, "projects_v2.db")
        super().__init__(db_path)
        self._create_tables()

    def _create_tables(self):
        """Tạo các bảng cần thiết để lưu trữ thông tin dự án nếu chưa tồn tại."""
        conn = self._get_connection()
        try:
            with conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS projects (
                        id INTEGER PRIMARY KEY AUTOINCREMENT, name TEXT NOT NULL UNIQUE,
                        description TEXT, f_folder TEXT, program_version TEXT,
                        created_timestamp REAL DEFAULT (strftime('%s', 'now')),
                        modified_timestamp REAL DEFAULT (strftime('%s', 'now'))
                    );
                """)
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS project_databases (
                        id INTEGER PRIMARY KEY AUTOINCREMENT, project_id INTEGER NOT NULL,
                        alias TEXT, type TEXT NOT NULL, server TEXT NOT NULL, name TEXT NOT NULL,
                        user TEXT NOT NULL, pass TEXT, db_version TEXT,
                        FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
                    );
                """)
        except sqlite3.Error as e:
            logger.error(f"[ProjectService] Lỗi khi tạo bảng: {e}")
        finally:
            conn.close()

    def load_projects(self) -> List[Dict[str, Any]]:
        """Tải tất cả các dự án và thông tin CSDL liên quan từ CSDL."""
        conn = self._get_connection()
        projects = []
        try:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("SELECT * FROM projects ORDER BY name COLLATE NOCASE")
            for row in cursor.fetchall():
                project = dict(row)
                db_cursor = conn.execute("SELECT * FROM project_databases WHERE project_id = ? ORDER BY type, alias", (project['id'],))
                project['databases'] = [dict(db_row) for db_row in db_cursor.fetchall()]
                projects.append(project)
            return projects
        except sqlite3.Error as e:
            logger.error(f"[ProjectService] Lỗi khi đọc projects từ SQLite: {e}")
            return []
        finally:
            conn.close()

    def save_projects(self, projects: List[Dict[str, Any]]):
        """Lưu toàn bộ danh sách dự án, đồng bộ hóa với CSDL."""
        conn = self._get_connection()
        try:
            with conn:
                cursor = conn.execute("SELECT id FROM projects")
                existing_ids = {row[0] for row in cursor.fetchall()}
                saved_ids = {self.upsert_project(p, conn) for p in projects if self.upsert_project(p, conn)}
                ids_to_delete = existing_ids - saved_ids
                if ids_to_delete:
                    conn.executemany("DELETE FROM projects WHERE id = ?", [(id,) for id in ids_to_delete])
        except sqlite3.Error as e:
            logger.error(f"[ProjectService] Lỗi khi ghi projects vào SQLite: {e}")
        finally:
            conn.close()
    
    def upsert_project(self, project: Dict[str, Any], conn: sqlite3.Connection) -> int:
        """Thêm mới hoặc cập nhật một dự án và các CSDL liên quan."""
        current_time = time.time()
        project_data = (project.get('name', 'Dự án không tên'), project.get('description', ''), project.get('f_folder', ''), project.get('program_version', ''), current_time)
        try:
            cursor = conn.execute("SELECT id FROM projects WHERE name = ?", (project['name'],))
            row = cursor.fetchone()
            if row:
                project_id = row[0]
                conn.execute("UPDATE projects SET description = ?, f_folder = ?, program_version = ?, modified_timestamp = ? WHERE id = ?", (project_data[1], project_data[2], project_data[3], project_data[4], project_id))
            else:
                cursor = conn.execute("INSERT INTO projects (name, description, f_folder, program_version, modified_timestamp) VALUES (?, ?, ?, ?, ?)", project_data)
                project_id = cursor.lastrowid
            
            conn.execute("DELETE FROM project_databases WHERE project_id = ?", (project_id,))
            if databases := project.get('databases', []):
                db_rows = [(project_id, db.get('alias'), db.get('type'), db.get('server'), db.get('name'), db.get('user'), db.get('pass'), db.get('db_version')) for db in databases]
                conn.executemany("INSERT INTO project_databases (project_id, alias, type, server, name, user, pass, db_version) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", db_rows)
            return project_id
        except sqlite3.Error as e:
            logger.error(f"[ProjectService] Lỗi upsert project '{project.get('name')}': {e}")
            return None

# ===================================================================
# SERVICE QUẢN LÝ CACHE DỮ LIỆU CHO TỪNG DỰ ÁN
# ===================================================================

class ProjectDataService(BaseSQLiteService):
    """Quản lý CSDL cache SQLite cho một dự án cụ thể."""
    def __init__(self, project_name: str):
        safe_filename = re.sub(r'[^\w\.-]', '_', project_name)
        db_path = os.path.join(SQLITE_CACHE_DIR, f"{safe_filename}.db")
        super().__init__(db_path)
        self._create_tables()

    def _create_tables(self):
        """Tạo tất cả các bảng cần thiết cho việc cache dữ liệu."""
        conn = self._get_connection()
        try:
            with conn:
                conn.execute("CREATE TABLE IF NOT EXISTS cached_files (path TEXT PRIMARY KEY, modified_timestamp REAL NOT NULL, entity_name TEXT);")
                conn.execute("CREATE TABLE IF NOT EXISTS file_properties (file_path TEXT, property_name TEXT NOT NULL, property_value TEXT, PRIMARY KEY (file_path, property_name), FOREIGN KEY (file_path) REFERENCES cached_files (path) ON DELETE CASCADE);")
                conn.execute("CREATE TABLE IF NOT EXISTS conditional_blocks (id INTEGER PRIMARY KEY AUTOINCREMENT, source_file TEXT NOT NULL, condition_flag TEXT, condition_state TEXT, UNIQUE(source_file, condition_flag, condition_state));")
                conn.execute("CREATE TABLE IF NOT EXISTS entity_structure (id INTEGER PRIMARY KEY AUTOINCREMENT, entity_name TEXT NOT NULL, entity_type TEXT NOT NULL, is_parameter BOOLEAN NOT NULL, is_external BOOLEAN NOT NULL, parent_entity TEXT, source_file TEXT NOT NULL, depth INTEGER, block_id INTEGER REFERENCES conditional_blocks(id) ON DELETE CASCADE);")
                conn.execute("CREATE TABLE IF NOT EXISTS entity_usage (id INTEGER PRIMARY KEY AUTOINCREMENT, file_path_using TEXT NOT NULL, entity_name_used TEXT NOT NULL, FOREIGN KEY (file_path_using) REFERENCES cached_files (path) ON DELETE CASCADE);")
                conn.execute("CREATE TABLE IF NOT EXISTS table_columns (id INTEGER PRIMARY KEY AUTOINCREMENT, db_name TEXT NOT NULL, table_name TEXT NOT NULL, column_name TEXT NOT NULL, ordinal_position INTEGER, data_type TEXT, max_length INTEGER, is_nullable TEXT, is_primary_key BOOLEAN, column_default TEXT, numeric_precision INTEGER, numeric_scale INTEGER, UNIQUE(db_name, table_name, column_name));")
                conn.execute("CREATE TABLE IF NOT EXISTS cached_fields (file_path TEXT PRIMARY KEY, fields_json TEXT, FOREIGN KEY (file_path) REFERENCES cached_files (path) ON DELETE CASCADE);")
                conn.execute("CREATE TABLE IF NOT EXISTS cached_forms (file_path TEXT PRIMARY KEY, forms_json TEXT, FOREIGN KEY (file_path) REFERENCES cached_files (path) ON DELETE CASCADE);")
            
            with conn:
                conn.execute("CREATE INDEX IF NOT EXISTS idx_entity_name ON cached_files (entity_name);")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_entity_structure_name ON entity_structure (entity_name);")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_entity_usage_name ON entity_usage (entity_name_used);")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_table_columns ON table_columns (db_name, table_name);")
        except sqlite3.Error as e:
            logger.error(f"[ProjectDataService] Lỗi khi tạo bảng: {e}")
        finally:
            conn.close()

    # --- Quản lý Cache File ---

    def update_cache_from_parser(self, file_path: str, analysis_result: Dict[str, Any]):
        """Cập nhật cache cho một file duy nhất từ kết quả phân tích."""
        try:
            mtime = os.path.getmtime(file_path)
        except FileNotFoundError:
            logger.warning(f"File không tồn tại, không thể cập nhật cache: {file_path}")
            return

        conn = self._get_connection()
        try:
            with conn:
                conn.execute("DELETE FROM cached_files WHERE path = ?", (file_path,))
                source_files = {e['source_file'] for e in analysis_result.get("entities", [])}
                if source_files:
                    conn.executemany("DELETE FROM entity_structure WHERE source_file = ?", [(sf,) for sf in source_files])
                    conn.executemany("DELETE FROM conditional_blocks WHERE source_file = ?", [(sf,) for sf in source_files])

                entity_name = analysis_result.get("dir_info", {}).get("name")
                conn.execute("INSERT INTO cached_files (path, modified_timestamp, entity_name) VALUES (?, ?, ?)", (file_path, mtime, entity_name))
                
                if props := [(file_path, k, str(v)) for k, v in analysis_result.get("dir_info", {}).items() if v is not None]:
                    conn.executemany("INSERT INTO file_properties (file_path, property_name, property_value) VALUES (?, ?, ?)", props)
                
                if entities := analysis_result.get("entities"):
                    self._cache_entities(conn, entities)

                if fields := analysis_result.get("fields"):
                    conn.execute("INSERT INTO cached_fields (file_path, fields_json) VALUES (?, ?)", (file_path, json.dumps(fields)))
                
                if forms := analysis_result.get("forms"):
                    conn.execute("INSERT INTO cached_forms (file_path, forms_json) VALUES (?, ?)", (file_path, json.dumps(forms)))

                if usage := [(file_path, name) for name in analysis_result.get("entity_references", [])]:
                    conn.executemany("INSERT INTO entity_usage (file_path_using, entity_name_used) VALUES (?, ?)", usage)
        except sqlite3.Error as e:
            logger.error(f"Lỗi khi cập nhật cache cho file '{file_path}': {e}", exc_info=True)
        finally:
            conn.close()

    def _cache_entities(self, conn: sqlite3.Connection, entities: List[Dict[str, Any]]):
        """Hàm helper để lưu thông tin entities vào CSDL."""
        unique_blocks = {(e['source_file'], e.get('condition_flag'), e.get('condition_state')) for e in entities if e.get('condition_flag')}
        if unique_blocks:
            conn.executemany("INSERT OR IGNORE INTO conditional_blocks (source_file, condition_flag, condition_state) VALUES (?, ?, ?)", list(unique_blocks))
        
        block_map = {(r[1], r[2], r[3]): r[0] for r in conn.execute("SELECT id, source_file, condition_flag, condition_state FROM conditional_blocks")}
        
        entity_rows = []
        for e in entities:
            key = (e['source_file'], e.get('condition_flag'), e.get('condition_state'))
            block_id = block_map.get(key) if e.get('condition_flag') else None
            entity_rows.append((e['entity_name'], e['entity_type'], e['is_parameter'], e['is_external'], e['parent_entity'], block_id, e['source_file'], e['depth']))
        conn.executemany("INSERT INTO entity_structure (entity_name, entity_type, is_parameter, is_external, parent_entity, block_id, source_file, depth) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", entity_rows)

    def get_analysis_info_from_cache(self, file_path: str) -> Dict[str, Any]:
        """Lấy toàn bộ thông tin phân tích đã lưu của một file từ cache."""
        conn = self._get_connection()
        conn.row_factory = sqlite3.Row
        try:
            if not conn.execute("SELECT 1 FROM cached_files WHERE path = ?", (file_path,)).fetchone():
                return {"analysis_failed": True, "dir_info": {"error": "File không có trong cache."}}

            info = {"analysis_failed": False, "dir_info": {}, "fields": [], "forms": [], "entities": [], "entity_references": [], "original_content": ""}
            
            for row in conn.execute("SELECT property_name, property_value FROM file_properties WHERE file_path = ?", (file_path,)):
                info["dir_info"][row["property_name"]] = row["property_value"]

            if row := conn.execute("SELECT fields_json FROM cached_fields WHERE file_path = ?", (file_path,)).fetchone():
                if row["fields_json"]: info["fields"] = json.loads(row["fields_json"])

            if row := conn.execute("SELECT forms_json FROM cached_forms WHERE file_path = ?", (file_path,)).fetchone():
                if row["forms_json"]: info["forms"] = json.loads(row["forms_json"])

            info["entities"] = [dict(row) for row in conn.execute("SELECT * FROM entity_structure WHERE source_file = ?", (file_path,))]
            info["entity_references"] = [row["entity_name_used"] for row in conn.execute("SELECT entity_name_used FROM entity_usage WHERE file_path_using = ?", (file_path,))]

            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    info["original_content"] = f.read()
            except IOError as e:
                logger.warning(f"Không thể đọc nội dung file từ đĩa: {file_path}, {e}")
            
            return info
        except (sqlite3.Error, json.JSONDecodeError) as e:
            logger.error(f"Lỗi khi đọc cache cho file '{file_path}': {e}", exc_info=True)
            return {"analysis_failed": True, "dir_info": {"error": f"Lỗi đọc cache: {e}"}}
        finally:
            conn.close()

    def get_files_to_reparse(self, disk_files: Dict[str, float]) -> List[str]:
        """
        So sánh danh sách file trên đĩa (đã quét) và cache để tìm các file cần phân tích lại.
        Hàm này chỉ thực hiện truy vấn CSDL, không quét đĩa.
        """
        conn = self._get_connection()
        try:
            cached_files = {row[0]: row[1] for row in conn.execute("SELECT path, modified_timestamp FROM cached_files")}
            
            files_to_reparse = [p for p, mt in disk_files.items() if p not in cached_files or cached_files.get(p, 0) < mt]
            deleted_files = [p for p in cached_files if p not in disk_files]
            
            if deleted_files:
                with conn:
                    conn.executemany("DELETE FROM cached_files WHERE path = ?", [(p,) for p in deleted_files])
            return files_to_reparse
        finally:
            conn.close()

    def get_all_files_in_project(self, folder_path: str) -> Dict[str, float]:
        """
        Quét đĩa để lấy danh sách tất cả các file hợp lệ và thời gian sửa đổi của chúng,
        loại bỏ các thư mục và file không cần thiết để tăng tốc.
        Hàm này thực hiện I/O và nên được gọi từ luồng nền.
        """
        disk_files = {}
        if not os.path.isdir(folder_path):
            return disk_files
            
        root_path = os.path.normpath(folder_path)
        
        for dirpath, dirnames, filenames in os.walk(root_path):
            # Tối ưu hóa: Loại bỏ các thư mục bị loại trừ khỏi danh sách duyệt tiếp
            dirnames[:] = [d for d in dirnames if not self._is_path_excluded(os.path.join(dirpath, d), root_path)]

            for filename in filenames:
                full_path = os.path.normpath(os.path.join(dirpath, filename))
                # Chỉ xử lý các file có extension hợp lệ và không bị loại trừ
                if filename.lower().endswith(FILE_EXTENSIONS) and not self._is_path_excluded(full_path, root_path, is_dir=False):
                    try:
                        disk_files[full_path] = os.path.getmtime(full_path)
                    except FileNotFoundError:
                        continue
        return disk_files

    def _is_path_excluded(self, path: str, root_folder: str, is_dir: bool = True) -> bool:
        """Kiểm tra xem một đường dẫn file hoặc thư mục có nên bị loại trừ không."""
        normalized_path = os.path.normpath(path)
        
        # 1. Loại trừ theo extension file (chỉ áp dụng cho file)
        if not is_dir and any(normalized_path.lower().endswith(ext) for ext in EXCLUDED_FILE_EXTENSIONS):
            return True
            
        # 2. Loại trừ theo thư mục
        try:
            relative_path = os.path.relpath(normalized_path, root_folder)
            relative_path_std = relative_path.replace(os.sep, '/')
            
            for excluded_folder in EXCLUDED_FOLDERS:
                excluded_folder_std = excluded_folder.strip('/\\').replace('\\', '/')
                if relative_path_std.startswith(excluded_folder_std):
                    return True
        except ValueError:
            pass
            
        return False

    def get_all_dir_info(self) -> Dict[str, Dict[str, str]]:
        """Lấy tất cả thông tin `dir_info` từ cache."""
        all_info = defaultdict(dict)
        conn = self._get_connection()
        try:
            for file_path, key, value in conn.execute("SELECT file_path, property_name, property_value FROM file_properties"):
                all_info[file_path][key] = value
            return dict(all_info)
        finally:
            conn.close()

    def get_files_with_document_id(self) -> dict:
        """Lấy các file có mã chứng từ (ID)."""
        conn = self._get_connection()
        try:
            cursor = conn.execute("SELECT file_path, property_value FROM file_properties WHERE property_name = 'id' AND property_value IS NOT NULL AND property_value != ''")
            return {os.path.normpath(row[0]): row[1] for row in cursor.fetchall()}
        finally:
            conn.close()

    # --- Quản lý Cache Entity ---

    def get_entity_usage(self, entity_name: str) -> List[str]:
        """Lấy danh sách các file sử dụng một entity cụ thể."""
        conn = self._get_connection()
        try:
            cursor = conn.execute("SELECT DISTINCT file_path_using FROM entity_usage WHERE entity_name_used = ? ORDER BY file_path_using", (entity_name,))
            return [row[0] for row in cursor.fetchall()]
        finally:
            conn.close()

    def get_entity_structure(self) -> List[Dict[str, Any]]:
        """Lấy toàn bộ cấu trúc entity đã được cache."""
        conn = self._get_connection()
        conn.row_factory = sqlite3.Row
        try:
            query = "SELECT e.*, b.condition_flag, b.condition_state FROM entity_structure e LEFT JOIN conditional_blocks b ON e.block_id = b.id ORDER BY e.entity_name, e.source_file, e.depth"
            return [dict(row) for row in conn.execute(query).fetchall()]
        finally:
            conn.close()

    # --- Quản lý Cache Menu ---

    def save_menu_to_cache(self, menu_df: pd.DataFrame):
        if menu_df is None or menu_df.empty: return
        import decimal
        # Chuyển đổi tất cả giá trị decimal.Decimal sang float
        menu_df = menu_df.applymap(lambda x: float(x) if isinstance(x, decimal.Decimal) else x)
        conn = self._get_connection()
        try:
            with conn: menu_df.to_sql('wcommand_cache', conn, if_exists='replace', index=False)
        finally:
            conn.close()

    def load_menu_from_cache(self) -> pd.DataFrame:
        conn = self._get_connection()
        try:
            return pd.read_sql_query("SELECT * FROM wcommand_cache", conn)
        except (pd.io.sql.DatabaseError, sqlite3.OperationalError):
            return pd.DataFrame()
        finally:
            conn.close()

    # --- Quản lý Cache CSDL ---

    def save_table_columns(self, db_name: str, table_name: str, columns_data: List[Dict[str, Any]]):
        if not columns_data: return
        conn = self._get_connection()
        try:
            with conn:
                conn.execute("DELETE FROM table_columns WHERE db_name = ? AND table_name = ?", (db_name, table_name))
                rows = [ (db_name, table_name, c.get('column_name'), c.get('ordinal_position'), c.get('data_type'), c.get('max_length') or c.get('character_maximum_length'), c.get('is_nullable'), c.get('is_primary_key'), str(c.get('column_default')), c.get('numeric_precision'), c.get('numeric_scale')) for c in columns_data ]
                conn.executemany("INSERT INTO table_columns (db_name, table_name, column_name, ordinal_position, data_type, max_length, is_nullable, is_primary_key, column_default, numeric_precision, numeric_scale) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", rows)
        finally:
            conn.close()

    def get_table_columns(self, db_name: str, table_name: str) -> List[Dict[str, Any]]:
        """Lấy thông tin cột của một bảng từ cache."""
        conn = self._get_connection()
        conn.row_factory = sqlite3.Row
        try:
            cursor = conn.execute("SELECT * FROM table_columns WHERE db_name = ? AND table_name = ? ORDER BY ordinal_position", (db_name, table_name))
            return [dict(row) for row in cursor.fetchall()]
        finally:
            conn.close()

    def get_all_cached_tables_and_views(self, db_name: str) -> List[str]:
        """Lấy danh sách các bảng/view đã có trong cache cho một CSDL."""
        conn = self._get_connection()
        try:
            return [row[0] for row in conn.execute("SELECT DISTINCT table_name FROM table_columns WHERE db_name = ?", (db_name,))]
        finally:
            conn.close()

    def clear_all_table_columns_cache(self):
        """Xóa toàn bộ cache thông tin cột CSDL."""
        conn = self._get_connection()
        try:
            with conn: conn.execute("DELETE FROM table_columns;")
            logger.info("Đã xóa toàn bộ cache thông tin cột CSDL.")
        finally:
            conn.close()
            
    # --- Các hàm xử lý hàng loạt ---

    def bulk_update_cache_from_parser(self, results: List[Tuple[str, Dict[str, Any]]]):
        """Cập nhật cache cho nhiều file trong một giao dịch duy nhất."""
        conn = self._get_connection()
        try:
            with conn:
                for file_path, analysis_result in results:
                    # Tái sử dụng logic cập nhật đơn lẻ bên trong transaction
                    self.update_cache_from_parser(file_path, analysis_result)
        finally:
            conn.close()
            
    def bulk_save_table_columns(self, results: List[Tuple[str, str, List[Dict[str, Any]]]]):
        """Lưu cache cột cho nhiều bảng trong một giao dịch duy nhất."""
        conn = self._get_connection()
        try:
            with conn:
                for db_name, table_name, columns in results:
                    if columns:
                        self.save_table_columns(db_name, table_name, columns)
        finally:
            conn.close()
