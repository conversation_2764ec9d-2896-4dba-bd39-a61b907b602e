# project_ui.py
"""
Giao diện quản lý dự án (Project).
Hoạt động như một cửa sổ phụ (Toplevel), kế thừa theme và style từ cửa sổ chính.
ĐÃ ĐƯỢC CẬP NHẬT: Chứ<PERSON> năng lấy thông tin tự động đã đượ<PERSON> chuy<PERSON> sang luồng riêng để tránh treo UI.
"""
import copy
import os
import threading
import xml.etree.ElementTree as ET
from tkinter import messagebox, filedialog

import ttkbootstrap as tb
from ttkbootstrap.constants import *
from ttkbootstrap.scrolled import ScrolledText

from constants import *
from sqlite_service import ProjectService
from utils_ui import setup_placeholder, StatusBar, load_window_state, setup_window_state_saving, center_dialog
import logging

logger = logging.getLogger(__name__)

# Mock service để UI có thể chạy độc lập
try:
    from mssql_service import MSSQLService
except ImportError:
    logger.warning("Không tìm thấy mssql_service, sử dụng MockMSSQLService.")
    class MockMSSQLService:
        @staticmethod
        def get_app_databases(server, db, user, pwd): return None
        @staticmethod
        def get_program_version(server, db, user, pwd): return "mock.version"
        @staticmethod
        def get_db_version(server, user, pwd): return "Mock SQL Server"
    MSSQLService = MockMSSQLService


def parse_connection_string(conn_str):
    """
    Phân tích chuỗi kết nối thành một dictionary.
    Hỗ trợ nhiều định dạng connection string khác nhau.
    """
    conn_dict = {}
    if not conn_str:
        return conn_dict

    conn_str = conn_str.strip()
    parts = conn_str.split(';')
    for part in parts:
        part = part.strip()
        if not part:
            continue

        if '=' in part:
            key, value = part.split('=', 1)
            key = key.strip().lower()
            value = value.strip()

            if value.startswith('"') and value.endswith('"'):
                value = value[1:-1]
            elif value.startswith("'") and value.endswith("'"):
                value = value[1:-1]

            conn_dict[key] = value

    return conn_dict


class DbDialog(tb.Toplevel):
    """Hộp thoại để thêm hoặc sửa thông tin một Database."""
    def __init__(self, parent, db_info=None):
        super().__init__(parent)
        self.parent = parent
        self.db_info = None
        title = "Sửa Database" if db_info else "Thêm Database"
        self.title(title)
        self.geometry("450x330")
        self.transient(parent)
        center_dialog(self, parent)

        self.var_type = tb.StringVar(value=db_info.get("type", "App") if db_info else "App")
        self.var_server = tb.StringVar(value=db_info.get("server", "") if db_info else "")
        self.var_name = tb.StringVar(value=db_info.get("name", "") if db_info else "")
        self.var_user = tb.StringVar(value=db_info.get("user", "") if db_info else "")
        self.var_pass = tb.StringVar(value=db_info.get("pass", "") if db_info else "")
        self.var_alias = tb.StringVar(value=db_info.get("alias", "") if db_info else "")

        main_frame = tb.Frame(self, padding=PANEL_PADDING * 3)
        main_frame.pack(fill=BOTH, expand=YES)
        main_frame.grid_columnconfigure(1, weight=1)

        fields = [
            ("Phân loại:", self.var_type, ["Sys", "App"]),
            ("Server:", self.var_server, "Bắt buộc"),
            ("DB Name:", self.var_name, "Bắt buộc"),
            ("User:", self.var_user, "Bắt buộc"),
            ("Password:", self.var_pass, ""),
            ("Alias (Tên gợi nhớ):", self.var_alias, "Tùy chọn"),
        ]

        for i, (label, var, placeholder_or_values) in enumerate(fields):
            tb.Label(main_frame, text=label).grid(row=i, column=0, sticky=W, pady=FIELD_PADDING_Y, padx=(0, 10))
            if isinstance(placeholder_or_values, list):
                combo = tb.Combobox(main_frame, textvariable=var, values=placeholder_or_values, state="readonly")
                combo.grid(row=i, column=1, sticky="ew")
            else:
                show_char = "*" if label == "Password:" else ""
                entry = tb.Entry(main_frame, textvariable=var, show=show_char)
                entry.grid(row=i, column=1, sticky="ew")
                if placeholder_or_values:
                    setup_placeholder(entry, var, placeholder_or_values)

        btn_frame = tb.Frame(self, padding=(0, PANEL_PADDING, 0, PANEL_PADDING * 2))
        btn_frame.pack(side=BOTTOM, fill=X)
        tb.Button(btn_frame, text="Hủy", command=self.destroy, bootstyle=f"{COLOR_SECONDARY}-outline").pack(side=RIGHT, padx=(PANEL_PADDING, 0))
        tb.Button(btn_frame, text="Lưu", command=self.on_save, bootstyle=COLOR_SUCCESS).pack(side=RIGHT)

        self.grab_set()

    def on_save(self):
        if not self.var_server.get() or not self.var_name.get() or not self.var_user.get():
            messagebox.showerror("Thiếu thông tin", "Các trường Server, DB Name, và User là bắt buộc.", parent=self)
            return
        self.db_info = {
            "type": self.var_type.get(), "server": self.var_server.get().strip(),
            "name": self.var_name.get().strip(), "user": self.var_user.get().strip(),
            "pass": self.var_pass.get(), "alias": self.var_alias.get().strip()
        }
        self.destroy()

    def show(self):
        self.wait_window()
        return self.db_info

class ProjectManagerTopbar(tb.Frame):
    """Thanh công cụ trên cùng đơn giản cho cửa sổ Quản lý Dự án."""
    def __init__(self, master, on_close_callback):
        super().__init__(master)
        left_frame = tb.Frame(self)
        right_frame = tb.Frame(self)
        left_frame.pack(side=LEFT, fill=X, expand=True, padx=(PANEL_PADDING, 0))
        right_frame.pack(side=RIGHT, padx=(0, PANEL_PADDING))

        tb.Label(left_frame, text="Quản lý Dự án ERP", font=(FONT_FAMILY, FONT_SIZE_IN_POINTS, "bold")).pack(side=LEFT)
        tb.Button(right_frame, text="Đóng", command=on_close_callback, bootstyle=f"{COLOR_SECONDARY}-outline").pack(side=LEFT, padx=5)

        self.pack(side=TOP, fill=X, pady=(PANEL_PADDING, 0))


class ProjectManagerUI(tb.Toplevel):
    """Cửa sổ để quản lý (thêm, sửa, xóa) các dự án."""
    def __init__(self, parent):
        super().__init__(parent)
        self.title("Quản lý Dự án ERP")
        self.transient(parent)

        self.font_normal = (FONT_FAMILY, FONT_SIZE_IN_POINTS)
        self.font_bold = (FONT_FAMILY, FONT_SIZE_IN_POINTS, "bold")

        if not load_window_state(self, window_key="ProjectManager"):
             self.geometry("1400x900")
             center_dialog(self, parent)
        setup_window_state_saving(self, window_key="ProjectManager")

        self.project_service = ProjectService()
        self.projects = self.project_service.load_projects()
        self.current_databases = []
        self.selected_idx = None
        self.is_fetching_auto = False

        self.create_widgets()
        self.refresh_projects()
        self.update_styles()

        self.protocol("WM_DELETE_WINDOW", self.on_close)

        if not self.projects:
            self.show_project(None)
        
        self.focus_set()
        self.grab_set()

    def create_widgets(self):
        """Tạo và sắp xếp các widget chính trên cửa sổ."""
        self.topbar = ProjectManagerTopbar(self, self.on_close)
        self.statusbar = StatusBar(self)
        self.statusbar.pack(side=BOTTOM, fill=X)

        main_pane = tb.Panedwindow(self, orient=HORIZONTAL)
        main_pane.pack(fill=BOTH, expand=1, padx=PANEL_PADDING, pady=PANEL_PADDING)

        self.create_left_panel(main_pane)
        self.create_right_panel(main_pane)

    def update_styles(self):
        """Cập nhật style cho các thành phần đặc thù của cửa sổ này."""
        self.style.map('Treeview', background=[('selected', self.style.colors.primary)])
        self.style.map('Treeview', foreground=[('selected', self.style.colors.selectfg)])
        if hasattr(self, 'tree_databases'):
             self._refresh_db_tree()

    def on_close(self):
        """Xử lý việc đóng cửa sổ Project Manager."""
        logger.info("Đóng cửa sổ Quản lý Dự án")
        self.destroy()

    def create_left_panel(self, parent):
        """Tạo khung bên trái chứa danh sách dự án và các nút hành động."""
        left_panel = tb.Frame(parent, padding=0)
        parent.add(left_panel, weight=1)

        lf_projects = tb.Labelframe(left_panel, text="Danh sách dự án", bootstyle=FRAME_STYLE, padding=PANEL_PADDING)
        lf_projects.pack(fill=BOTH, expand=True)

        tree_frame = tb.Frame(lf_projects)
        tree_frame.pack(fill=BOTH, expand=1, pady=(0, PANEL_PADDING))
        self.tree_projects = tb.Treeview(tree_frame, columns=("name",), show="headings", bootstyle=COLOR_PRIMARY)
        self.tree_projects.heading("name", text="Tên dự án")
        self.tree_projects.pack(side=LEFT, fill=BOTH, expand=1)

        scrollbar = tb.Scrollbar(tree_frame, orient=VERTICAL, command=self.tree_projects.yview, bootstyle=f"{COLOR_PRIMARY}-round")
        self.tree_projects.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.tree_projects.bind("<<TreeviewSelect>>", self.on_select_project)

        btns_frame = tb.Frame(lf_projects)
        btns_frame.pack(fill=X)
        tb.Button(btns_frame, text="Tạo Mới", command=self.add_project, bootstyle=COLOR_SUCCESS).pack(side=LEFT, expand=True, fill=X, padx=(0, 2))
        tb.Button(btns_frame, text="Xóa Dự Án", command=self.delete_project, bootstyle=COLOR_DANGER).pack(side=RIGHT, expand=True, fill=X, padx=(2, 0))

    def create_right_panel(self, parent):
        """Tạo khung chứa bên phải, bao gồm cả trạng thái trống và form chi tiết."""
        right_panel_container = tb.Frame(parent, padding=0)
        parent.add(right_panel_container, weight=3)
        right_panel_container.grid_rowconfigure(0, weight=1)
        right_panel_container.grid_columnconfigure(0, weight=1)

        self.right_panel_empty = tb.Frame(right_panel_container)
        self.right_panel_empty.grid(row=0, column=0, sticky="nsew")
        tb.Label(self.right_panel_empty, text="Vui lòng chọn một dự án hoặc tạo dự án mới", font=self.font_bold, bootstyle=COLOR_SECONDARY).pack(expand=True)

        self.right_panel_form = tb.Frame(right_panel_container)
        self.right_panel_form.grid(row=0, column=0, sticky="nsew")

        self._create_details_form(self.right_panel_form)

    def _create_details_form(self, parent):
        """Tạo nội dung chi tiết cho form dự án."""
        self.lf_details = tb.Labelframe(parent, text="Chi tiết dự án", bootstyle=FRAME_STYLE, padding=PANEL_PADDING)
        self.lf_details.pack(fill=BOTH, expand=True)

        action_bar = tb.Frame(self.lf_details, padding=(PANEL_PADDING, PANEL_PADDING))
        action_bar.pack(side=BOTTOM, fill=X)
        tb.Button(action_bar, text="Lưu Thay Đổi", command=self.save_project, bootstyle=COLOR_SUCCESS, width=BUTTON_WIDTH).pack(side=RIGHT)

        form_fields_frame = tb.Frame(self.lf_details, padding=(PANEL_PADDING, PANEL_PADDING))
        form_fields_frame.pack(side=TOP, fill=BOTH, expand=YES, padx=5)

        self._create_general_info_frame(form_fields_frame)
        self._create_databases_frame(form_fields_frame)

    def _create_general_info_frame(self, parent):
        """Tạo khung thông tin chung."""
        self.var_name = tb.StringVar()
        self.var_program_version = tb.StringVar()
        self.var_f_folder = tb.StringVar()

        lf_general = tb.Labelframe(parent, text="Thông tin chung", bootstyle=FRAME_STYLE, padding=PANEL_PADDING * 2)
        lf_general.pack(fill=X, expand=False, pady=(0, PANEL_PADDING * 2))
        lf_general.grid_columnconfigure(1, weight=1)

        name_frame = tb.Frame(lf_general)
        name_frame.grid(row=0, column=0, columnspan=2, sticky="ew", pady=FIELD_PADDING_Y)
        name_frame.grid_columnconfigure(0, weight=1)

        tb.Label(name_frame, text="Tên dự án:").grid(row=0, column=0, sticky=W)
        entry_name = tb.Entry(name_frame, textvariable=self.var_name)
        entry_name.grid(row=0, column=1, sticky="ew", padx=(FIELD_PADDING_X, 10))
        setup_placeholder(entry_name, self.var_name, "Bắt buộc: Nhập tên dự án")

        tb.Label(name_frame, text="Phiên bản CT:").grid(row=0, column=2, sticky=W)
        entry_version = tb.Entry(name_frame, textvariable=self.var_program_version, state="readonly", width=15)
        entry_version.grid(row=0, column=3, sticky="w", padx=(FIELD_PADDING_X, 0))

        tb.Label(lf_general, text="Thư mục .f:").grid(row=1, column=0, sticky="w", pady=FIELD_PADDING_Y)
        folder_frame = tb.Frame(lf_general)
        folder_frame.grid(row=1, column=1, sticky="ew", padx=(FIELD_PADDING_X, 0))
        folder_frame.grid_columnconfigure(0, weight=1)

        entry_f_folder = tb.Entry(folder_frame, textvariable=self.var_f_folder)
        entry_f_folder.grid(row=0, column=0, sticky="ew")
        setup_placeholder(entry_f_folder, self.var_f_folder, "Dán đường dẫn thư mục...")

        tb.Button(folder_frame, text="Chọn...", command=self.browse_folder, bootstyle=f"{COLOR_INFO}-outline").grid(row=0, column=1, padx=(5, 0))
        self.btn_fetch_auto = tb.Button(folder_frame, text="Lấy thông tin tự động", command=self.start_fetch_all_info, bootstyle=f"{COLOR_SECONDARY}-outline")
        self.btn_fetch_auto.grid(row=0, column=2, padx=(5, 0))

        tb.Label(lf_general, text="Mô tả:").grid(row=2, column=0, sticky="nw", pady=FIELD_PADDING_Y)
        self.txt_desc = ScrolledText(lf_general, height=2, font=self.font_normal, autohide=True, wrap='word', bootstyle=FRAME_STYLE)
        self.txt_desc.grid(row=2, column=1, sticky="ew", padx=(FIELD_PADDING_X, 0))

    def _create_databases_frame(self, parent):
        """Tạo khung cấu hình Databases."""
        lf_db = tb.Labelframe(parent, text="Cấu hình Databases", bootstyle=FRAME_STYLE, padding=PANEL_PADDING * 2)
        lf_db.pack(fill=X, expand=False, pady=(PANEL_PADDING * 2, PANEL_PADDING * 2))
        lf_db.grid_columnconfigure(0, weight=1)

        self.tree_databases = tb.Treeview(lf_db, columns=("type", "alias", "name", "server", "db_version"), show="headings", height=6, bootstyle=COLOR_PRIMARY)
        self.tree_databases.grid(row=0, column=0, sticky="nsew")
        self.tree_databases.heading("type", text="Phân loại")
        self.tree_databases.heading("alias", text="Tên Gợi nhớ")
        self.tree_databases.heading("name", text="Tên Database")
        self.tree_databases.heading("server", text="Server")
        self.tree_databases.heading("db_version", text="Phiên bản DB")
        self.tree_databases.column("type", width=80, anchor=W)
        self.tree_databases.column("alias", width=150, anchor=W)
        self.tree_databases.column("name", width=150, anchor=W)
        self.tree_databases.column("server", width=150, anchor=W)
        self.tree_databases.column("db_version", width=200, anchor=W)
        self.tree_databases.tag_configure('Sys', font=self.font_bold)

        db_scroll = tb.Scrollbar(lf_db, orient=VERTICAL, command=self.tree_databases.yview, bootstyle=f"{COLOR_PRIMARY}-round")
        db_scroll.grid(row=0, column=1, sticky="ns")
        self.tree_databases.configure(yscrollcommand=db_scroll.set)

        db_actions = tb.Frame(lf_db)
        db_actions.grid(row=1, column=0, columnspan=2, sticky="e", pady=(PANEL_PADDING, 0))
        tb.Button(db_actions, text="Thêm...", command=self.add_db, bootstyle=f"{COLOR_SUCCESS}-outline").pack(side=LEFT, padx=2)
        tb.Button(db_actions, text="Sửa...", command=self.edit_db, bootstyle=f"{COLOR_INFO}-outline").pack(side=LEFT, padx=2)
        tb.Button(db_actions, text="Xóa", command=self.delete_db, bootstyle=f"{COLOR_DANGER}-outline").pack(side=LEFT, padx=2)

    def show_project(self, idx):
        is_valid_idx = idx is not None and 0 <= idx < len(self.projects)
        if hasattr(self, 'right_panel_form'):
            if is_valid_idx:
                self.right_panel_form.grid()
                self.right_panel_empty.grid_remove()
                p = self.projects[idx]
                self.lf_details.config(text=f" Chi tiết dự án: {p.get('name', '')} ")
                self.var_name.set(p.get("name", ""))
                self.var_f_folder.set(p.get("f_folder", ""))
                self.var_program_version.set(p.get("program_version", ""))
                self.txt_desc.text.delete("1.0", END)
                self.txt_desc.text.insert("1.0", p.get("description", ""))
                self.current_databases = copy.deepcopy(p.get("databases", []))
                self._refresh_db_tree()
            else:
                self.right_panel_empty.grid()
                self.right_panel_form.grid_remove()
                self.selected_idx = None

    def refresh_projects(self):
        self.tree_projects.delete(*self.tree_projects.get_children())
        for p in self.projects:
            self.tree_projects.insert("", "end", values=(p.get("name", "Dự án không tên"),))
        if self.projects:
            first_item = self.tree_projects.get_children()[0]
            self.tree_projects.selection_set(first_item)
            self.tree_projects.focus(first_item)

    def on_select_project(self, _=None):
        if not self.tree_projects.selection():
            self.selected_idx = None
            self.show_project(None)
            return
        item_id = self.tree_projects.selection()[0]
        self.selected_idx = self.tree_projects.index(item_id)
        self.show_project(self.selected_idx)

    def add_project(self):
        new_project = {"name": "Dự án mới", "description": "", "databases": [], "program_version": ""}
        self.projects.append(new_project)
        self.project_service.save_projects(self.projects)
        self.refresh_projects()
        idx = len(self.projects) - 1
        new_item_id = self.tree_projects.get_children()[idx]
        self.tree_projects.selection_set(new_item_id)
        self.tree_projects.focus(new_item_id)
        self.tree_projects.see(new_item_id)
        self.statusbar.show_message("Đã tạo dự án mới. Hãy điền thông tin và lưu lại.")

    def save_project(self):
        if self.selected_idx is None:
            messagebox.showwarning("Chưa chọn dự án", "Vui lòng chọn một dự án để lưu.", parent=self)
            return
        p = self.projects[self.selected_idx]
        name_val = self.var_name.get().strip()
        if not name_val or name_val == "Bắt buộc: Nhập tên dự án":
            messagebox.showerror("Lỗi", "Tên dự án không được để trống.", parent=self)
            return
        p.update({
            "name": name_val, "description": self.txt_desc.text.get("1.0", END).strip(),
            "f_folder": self.var_f_folder.get().strip(), "program_version": self.var_program_version.get().strip(),
            "databases": self.current_databases
        })
        for old_key in ["db_sys", "db_apps", "db_server", "db_user", "db_pass", "db_name"]:
            p.pop(old_key, None)
        self.project_service.save_projects(self.projects)
        current_selection_index = self.selected_idx
        self.refresh_projects()
        if current_selection_index < len(self.tree_projects.get_children()):
            item_to_select = self.tree_projects.get_children()[current_selection_index]
            self.tree_projects.selection_set(item_to_select)
            self.tree_projects.focus(item_to_select)
        self.statusbar.show_message(f"Đã lưu dự án '{p['name']}' thành công!")

    def delete_project(self):
        if self.selected_idx is None:
            self.statusbar.show_message("Vui lòng chọn một dự án để xóa.", 3000)
            return
        project_name = self.projects[self.selected_idx].get('name', 'không tên')
        if messagebox.askyesno("Xóa dự án", f"Bạn có chắc chắn muốn xóa dự án '{project_name}' không?", parent=self):
            self.projects.pop(self.selected_idx)
            self.project_service.save_projects(self.projects)
            self.refresh_projects()
            self.show_project(None)
            self.statusbar.show_message(f"Đã xóa dự án '{project_name}'.")

    def _refresh_db_tree(self):
        self.tree_databases.delete(*self.tree_databases.get_children())
        for i, db in enumerate(self.current_databases):
            tags = ('Sys',) if db.get("type") == 'Sys' else ()
            alias = db.get("alias") or f"Database {i+1}"
            self.tree_databases.insert("", "end", iid=i, values=(db.get("type", "App"), alias, db.get("name", ""), db.get("server", ""), db.get("db_version", "")), tags=tags)

    def add_db(self):
        dialog = DbDialog(self)
        new_db_info = dialog.show()
        if new_db_info:
            if new_db_info['type'] == 'Sys' and any(db['type'] == 'Sys' for db in self.current_databases):
                messagebox.showerror("Lỗi", "Mỗi dự án chỉ có thể có một Sys Database.", parent=self)
                return
            if any(db['name'] == new_db_info['name'] and db['server'] == new_db_info['server'] for db in self.current_databases):
                messagebox.showwarning("Trùng lặp", f"Database '{new_db_info['name']}' trên server '{new_db_info['server']}' đã tồn tại.", parent=self)
                return
            self.current_databases.append(new_db_info)
            self._refresh_db_tree()

    def edit_db(self):
        selected_item = self.tree_databases.focus()
        if not selected_item:
            messagebox.showwarning("Chưa chọn", "Vui lòng chọn một Database để sửa.", parent=self)
            return
        item_index = int(selected_item)
        dialog = DbDialog(self, db_info=self.current_databases[item_index])
        updated_db_info = dialog.show()
        if updated_db_info:
            if updated_db_info['type'] == 'Sys' and any(db['type'] == 'Sys' and i != item_index for i, db in enumerate(self.current_databases)):
                messagebox.showerror("Lỗi", "Mỗi dự án chỉ có thể có một Sys Database.", parent=self)
                return
            self.current_databases[item_index] = updated_db_info
            self._refresh_db_tree()

    def delete_db(self):
        selected_item = self.tree_databases.focus()
        if not selected_item:
            messagebox.showwarning("Chưa chọn", "Vui lòng chọn một Database để xóa.", parent=self)
            return
        if messagebox.askyesno("Xác nhận xóa", "Bạn có chắc muốn xóa Database này không?", parent=self):
            del self.current_databases[int(selected_item)]
            self._refresh_db_tree()

    def browse_folder(self):
        if path := filedialog.askdirectory(title="Chọn thư mục chứa file .f", parent=self):
            self.var_f_folder.set(path)

    # ===================================================================
    # Auto Fetch Logic (Threaded)
    # ===================================================================
    def start_fetch_all_info(self):
        """Kích hoạt quá trình lấy thông tin tự động trong một luồng riêng."""
        if self.is_fetching_auto:
            self.statusbar.show_message("Đang trong quá trình lấy thông tin...", 3000)
            return
        if self.selected_idx is None:
            messagebox.showwarning("Chưa chọn dự án", "Vui lòng chọn một dự án để lấy thông tin.", parent=self)
            return
        
        self.is_fetching_auto = True
        self.btn_fetch_auto.config(state=DISABLED)
        self.statusbar.show_message("Bắt đầu lấy thông tin tự động...", 10000)
        
        # Chạy tác vụ nền
        thread = threading.Thread(target=self._background_fetch_task, daemon=True)
        thread.start()

    def _background_fetch_task(self):
        """Hàm này chạy trong luồng nền để thực hiện các tác vụ I/O."""
        try:
            # Step 1: Read web.config
            self.after(0, self.statusbar.show_message, "Đang đọc web.config...")
            sys_db_info, error = self._task_read_web_config()
            if error:
                self.after(0, messagebox.showerror, "Lỗi đọc file", error, parent=self)
                return 

            self.after(0, self._update_ui_after_web_config, sys_db_info)

            # Step 2: Fetch App Databases
            self.after(0, self.statusbar.show_message, "Đang lấy danh sách App DB...")
            app_dbs, error = self._task_fetch_app_databases(sys_db_info)
            if error:
                self.after(0, messagebox.showerror, "Lỗi kết nối", error, parent=self)
            else:
                self.after(0, self._update_ui_after_app_dbs, app_dbs, sys_db_info)

            # Step 3: Fetch Program Version
            self.after(0, self.statusbar.show_message, "Đang lấy phiên bản chương trình...")
            version, error = self._task_fetch_program_version()
            if error:
                self.after(0, self.statusbar.show_message, f"Lỗi: {error}", 4000)
            else:
                self.after(0, self.var_program_version.set, version)

            # Step 4: Fetch all DB versions
            self.after(0, self.statusbar.show_message, "Đang lấy phiên bản các DB...")
            server_versions, error = self._task_fetch_all_db_versions()
            if error:
                 self.after(0, self.statusbar.show_message, f"Lỗi: {error}", 4000)
            else:
                self.after(0, self._update_ui_after_db_versions, server_versions)
            
            self.after(0, self.statusbar.show_message, "Hoàn tất quá trình lấy thông tin tự động!", 5000)

        except Exception as e:
            logger.error(f"Lỗi nghiêm trọng trong luồng lấy thông tin: {e}", exc_info=True)
            self.after(0, messagebox.showerror, "Lỗi nghiêm trọng", f"Đã xảy ra lỗi không mong muốn:\n{e}", parent=self)
        finally:
            self.after(0, self._finalize_fetch_task)

    def _finalize_fetch_task(self):
        """Hàm được gọi trên luồng UI để dọn dẹp sau khi tác vụ nền kết thúc."""
        self.is_fetching_auto = False
        self.btn_fetch_auto.config(state=NORMAL)
    
    # --- UI Update Callbacks ---
    def _update_ui_after_web_config(self, sys_db_info):
        if not sys_db_info: return
        try:
            sys_db_index = next(i for i, db in enumerate(self.current_databases) if db['type'] == 'Sys')
            self.current_databases[sys_db_index] = sys_db_info
        except StopIteration:
            self.current_databases.insert(0, sys_db_info)
        self._refresh_db_tree()

    def _update_ui_after_app_dbs(self, app_db_list, sys_db_info):
        if not app_db_list: return
        existing_dbs = {(db['server'], db['name']) for db in self.current_databases}
        for fetched_db in app_db_list:
            new_app_db = {
                "type": "App", "server": sys_db_info.get('server', ''),
                "name": fetched_db.get('cdata'), "alias": fetched_db.get('cname'),
                "user": sys_db_info.get('user', ''), "pass": sys_db_info.get('pass', '')
            }
            if (new_app_db['server'], new_app_db['name']) not in existing_dbs:
                self.current_databases.append(new_app_db)
        self._refresh_db_tree()

    def _update_ui_after_db_versions(self, server_versions):
        if not server_versions: return
        for db in self.current_databases:
            server_name = db.get('server')
            if server_name in server_versions:
                db['db_version'] = server_versions[server_name]
        self._refresh_db_tree()

    # --- Individual Task Functions (for background thread) ---
    def _task_read_web_config(self):
        project_folder = self.var_f_folder.get()
        if not project_folder:
            return None, "Vui lòng chọn thư mục .f trước."
        web_config_path = os.path.join(project_folder, 'web.config')
        if not os.path.isfile(web_config_path):
            return None, f"Không tìm thấy file 'web.config' trong thư mục:\n{project_folder}"

        try:
            root = ET.parse(web_config_path).getroot()
        except ET.ParseError as e:
            return None, f"Lỗi XML, không thể đọc file web.config:\n{e}"
        except Exception as e:
            return None, f"Lỗi khi đọc web.config:\n{e}"

        connection_names = ['sysConnectionString', 'SystemConnectionString', 'DefaultConnection', 'ConnectionString']
        conn_str_node, found_name = None, None
        for name in connection_names:
            conn_str_node = next((add.get('connectionString') for add in root.findall('.//connectionStrings/add') if add.get('name') == name), None)
            if conn_str_node:
                found_name = name
                break

        if not conn_str_node:
            available = [add.get('name') for add in root.findall('.//connectionStrings/add') if add.get('name')]
            msg = f"Không tìm thấy connection string hệ thống trong web.config.\n\nTìm kiếm: {', '.join(connection_names)}"
            msg += f"\n\nCó sẵn: {', '.join(available)}" if available else "\n\nKhông có connection string nào."
            return None, msg

        conn_details = parse_connection_string(conn_str_node)
        server = conn_details.get('data source') or conn_details.get('server')
        database = conn_details.get('initial catalog') or conn_details.get('database')
        
        if not server or not database:
            return None, f"Connection string thiếu thông tin Server hoặc Database.\n\nString: {conn_str_node}"

        sys_db_info = {
            "type": "Sys", "server": server, "name": database,
            "user": conn_details.get('uid') or conn_details.get('user id') or '',
            "pass": conn_details.get('pwd') or conn_details.get('password') or '',
            "alias": f"Hệ thống (từ {found_name})"
        }
        logger.info(f"Đã đọc thành công connection string '{found_name}' từ web.config.")
        return sys_db_info, None

    def _task_fetch_app_databases(self, sys_db_info):
        if not sys_db_info:
            return None, "Thiếu cấu hình Sys Database để lấy danh sách App DB."
        
        app_db_list = MSSQLService.get_app_databases(
            server=sys_db_info['server'], db=sys_db_info['name'],
            user=sys_db_info['user'], pwd=sys_db_info['pass']
        )
        if app_db_list is None:
            return None, "Không thể kết nối tới Sys Database để lấy danh sách App DB."
        return app_db_list, None

    def _task_fetch_program_version(self):
        app_db_info = next((db for db in self.current_databases if db.get('type') == 'App'), None)
        if not app_db_info:
            return None, "Không tìm thấy App Database nào để lấy phiên bản chương trình."

        version = MSSQLService.get_program_version(
            server=app_db_info.get('server', ''), db=app_db_info.get('name', ''),
            user=app_db_info.get('user', ''), pwd=app_db_info.get('pass', '')
        )
        return (version, None) if version is not None else (None, "Không thể lấy phiên bản chương trình.")

    def _task_fetch_all_db_versions(self):
        unique_servers = {db.get('server'): {'user': db.get('user', ''), 'pass': db.get('pass', '')} for db in self.current_databases if db.get('server')}
        server_versions = {}
        for server, creds in unique_servers.items():
            version = MSSQLService.get_db_version(server, creds.get('user', ''), creds.get('pass', ''))
            if version:
                server_versions[server] = version
        return (server_versions, None) if server_versions else (None, "Không thể lấy phiên bản của các DB server.")