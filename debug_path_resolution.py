#!/usr/bin/env python3
"""
Debug script để kiểm tra path resolution logic
"""

import os

def debug_path_resolution():
    """Debug path resolution với case cụ thể"""
    
    # Case từ lỗi
    current_file = r"E:\FastBusinessOnline\App_Data\Controllers\Dir\AITran.f"
    relative_path = r"..\Include\Command\WhenVoucherBeforeEdit.txt"
    
    print(f"🔍 Debug Path Resolution")
    print(f"Current file: {current_file}")
    print(f"Relative path: {relative_path}")
    print(f"Current file exists: {os.path.exists(current_file)}")
    
    # Lấy directory của current file
    current_dir = os.path.dirname(current_file)
    print(f"Current directory: {current_dir}")
    
    # Resolve path
    resolved_path = os.path.normpath(os.path.join(current_dir, relative_path))
    print(f"Resolved path: {resolved_path}")
    print(f"Resolved path exists: {os.path.exists(resolved_path)}")
    
    # Ki<PERSON>m tra các thành phần path
    print(f"\n📂 Path components:")
    print(f"Current dir parts: {current_dir.split(os.sep)}")
    print(f"Relative path parts: {relative_path.split(os.sep)}")
    print(f"Resolved path parts: {resolved_path.split(os.sep)}")
    
    # Thử các cách resolve khác
    print(f"\n🔧 Alternative resolutions:")
    
    # 1. Thử với raw join
    alt1 = os.path.join(current_dir, relative_path)
    print(f"1. Raw join: {alt1}")
    print(f"   Exists: {os.path.exists(alt1)}")
    
    # 2. Thử với abspath
    alt2 = os.path.abspath(os.path.join(current_dir, relative_path))
    print(f"2. Abspath: {alt2}")
    print(f"   Exists: {os.path.exists(alt2)}")
    
    # 3. Kiểm tra expected path
    expected_dir = r"E:\FastBusinessOnline\App_Data\Controllers\Include\Command"
    expected_file = os.path.join(expected_dir, "WhenVoucherBeforeEdit.txt")
    print(f"3. Expected: {expected_file}")
    print(f"   Exists: {os.path.exists(expected_file)}")
    
    # 4. Kiểm tra directory structure
    print(f"\n📁 Directory structure check:")
    base_dir = r"E:\FastBusinessOnline\App_Data\Controllers"
    if os.path.exists(base_dir):
        print(f"Base dir exists: {base_dir}")
        
        include_dir = os.path.join(base_dir, "Include")
        print(f"Include dir exists: {os.path.exists(include_dir)}")
        
        if os.path.exists(include_dir):
            command_dir = os.path.join(include_dir, "Command")
            print(f"Command dir exists: {os.path.exists(command_dir)}")
            
            if os.path.exists(command_dir):
                target_file = os.path.join(command_dir, "WhenVoucherBeforeEdit.txt")
                print(f"Target file exists: {os.path.exists(target_file)}")
                
                # List files in command dir
                try:
                    files = os.listdir(command_dir)
                    print(f"Files in Command dir: {files[:10]}...")  # First 10 files
                except Exception as e:
                    print(f"Error listing Command dir: {e}")
    
    # 5. Thử tìm file với pattern matching
    print(f"\n🔍 Pattern matching search:")
    search_patterns = [
        "WhenVoucherBeforeEdit.txt",
        "whenvoucherbeforeedit.txt",
        "*WhenVoucher*",
        "*BeforeEdit*"
    ]
    
    search_dir = r"E:\FastBusinessOnline\App_Data\Controllers\Include"
    if os.path.exists(search_dir):
        import glob
        for pattern in search_patterns:
            matches = glob.glob(os.path.join(search_dir, "**", pattern), recursive=True)
            if matches:
                print(f"Pattern '{pattern}' found: {matches[:3]}...")  # First 3 matches

if __name__ == "__main__":
    debug_path_resolution()
