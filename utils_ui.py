# utils_ui.py
import os
import re
import xml.dom.minidom
import ttkbootstrap as tb
from ttkbootstrap.constants import *
from tkinter import TclError, Label, Menu
from typing import List, Dict, Any

from constants import *
import logging
logger = logging.getLogger(__name__)
import json

# =========================
# Config Service Logic
# =========================
# ... (giữ nguyên code cũ)
def load_config():
    """Tải cấu hình từ file config.json. Tr<PERSON> về config mặc định nếu có lỗi."""
    default_config = {
        'theme': DEFAULT_THEME,
        'window_states': {}
    }

    if not os.path.exists(CONFIG_FILE):
        return default_config

    try:
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            config = json.load(f)
            # <PERSON><PERSON><PERSON> bả<PERSON> c<PERSON> đầy đủ các key cần thiết
            if 'theme' not in config:
                config['theme'] = DEFAULT_THEME
            if 'window_states' not in config:
                config['window_states'] = {}
            logger.info(f"Đã tải cấu hình từ {CONFIG_FILE}")
            return config
    except (IOError, json.JSONDecodeError) as e:
        logger.error(f"Không thể đọc file config, sử dụng cấu hình mặc định: {e}")
        return default_config

def save_config(config):
    """Lưu cấu hình vào file config.json."""
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        logger.info(f"Đã lưu cấu hình vào {CONFIG_FILE}")
    except IOError as e:
        logger.error(f"Không thể ghi file config: {e}")

def save_theme(theme_name: str):
    """Lưu theme hiện tại vào file config.json."""
    try:
        config = load_config()
        config['theme'] = theme_name
        save_config(config)
        logger.info(f"Đã lưu theme '{theme_name}' vào {CONFIG_FILE}")
    except Exception as e:
        logger.error(f"Không thể lưu theme: {e}")

def load_theme() -> str:
    """Tải theme từ file config.json. Trả về theme mặc định nếu có lỗi."""
    try:
        config = load_config()
        theme = config.get('theme', DEFAULT_THEME)
        logger.info(f"Đã tải theme '{theme}' từ {CONFIG_FILE}")
        return theme
    except Exception as e:
        logger.error(f"Không thể đọc theme, sử dụng theme mặc định: {e}")
        return DEFAULT_THEME

# =========================
# Window State Service Logic
# =========================

def save_window_state(window):
    """Lưu trạng thái cửa sổ (vị trí, kích thước, state) vào file config.json."""
    try:
        # Lấy thông tin cửa sổ hiện tại
        geometry = window.geometry()  # Ví dụ: "1200x800+100+50"
        state = window.state()  # normal, zoomed, iconic, withdrawn

        # Parse geometry string
        size_pos = geometry.split('+')
        size = size_pos[0]  # "1200x800"
        pos_x = int(size_pos[1]) if len(size_pos) > 1 else 0
        pos_y = int(size_pos[2]) if len(size_pos) > 2 else 0

        width, height = map(int, size.split('x'))

        window_state = {
            'width': width,
            'height': height,
            'x': pos_x,
            'y': pos_y,
            'state': state,
            'title': window.title()
        }

        # Tải config hiện tại
        config = load_config()

        # Lưu state cho window này (dùng title làm key)
        window_key = window.title() or "main_window"
        config['window_states'][window_key] = window_state

        # Ghi lại file
        save_config(config)

        logger.info(f"Đã lưu trạng thái cửa sổ '{window_key}' vào {CONFIG_FILE}")

    except Exception as e:
        logger.error(f"Không thể lưu trạng thái cửa sổ: {e}")

def load_window_state(window, window_key=None):
    """Khôi phục trạng thái cửa sổ từ file config.json."""
    try:
        config = load_config()
        all_states = config.get('window_states', {})

        # Xác định key cho window
        if not window_key:
            window_key = window.title() or "main_window"

        if window_key not in all_states:
            logger.info(f"Không tìm thấy trạng thái cho cửa sổ '{window_key}'")
            return False

        state_data = all_states[window_key]

        # Validate dữ liệu
        width = max(state_data.get('width', DEFAULT_WINDOW_WIDTH), MIN_WINDOW_WIDTH)
        height = max(state_data.get('height', DEFAULT_WINDOW_HEIGHT), MIN_WINDOW_HEIGHT)
        x = max(state_data.get('x', 100), 0)
        y = max(state_data.get('y', 50), 0)
        window_state = state_data.get('state', 'normal')

        # Kiểm tra xem vị trí có hợp lệ không (trong màn hình)
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()

        if x + width > screen_width:
            x = max(0, screen_width - width)
        if y + height > screen_height:
            y = max(0, screen_height - height)

        # Áp dụng geometry
        geometry_str = f"{width}x{height}+{x}+{y}"
        window.geometry(geometry_str)

        # Áp dụng state (nhưng không áp dụng 'zoomed' ngay lập tức để tránh conflict)
        if window_state in ['normal', 'iconic', 'withdrawn']:
            window.state(window_state)
        elif window_state == 'zoomed':
            # Delay việc maximize để window có thời gian render
            window.after(100, lambda: window.state('zoomed'))

        logger.info(f"Đã khôi phục trạng thái cửa sổ '{window_key}': {geometry_str}, state: {window_state}")
        return True

    except Exception as e:
        logger.error(f"Không thể khôi phục trạng thái cửa sổ: {e}")
        return False

def setup_window_state_saving(window, window_key=None):
    """Thiết lập tự động lưu trạng thái cửa sổ khi đóng."""
    def on_window_close():
        save_window_state(window)
        window.destroy()

    # Override protocol đóng cửa sổ
    window.protocol("WM_DELETE_WINDOW", on_window_close)

def clear_window_state(window_key=None):
    """Xóa trạng thái cửa sổ đã lưu."""
    try:
        config = load_config()
        all_states = config.get('window_states', {})

        if window_key:
            # Xóa một window cụ thể
            if window_key in all_states:
                del all_states[window_key]
                logger.info(f"Đã xóa trạng thái cửa sổ '{window_key}'")
            else:
                logger.info(f"Không tìm thấy trạng thái cửa sổ '{window_key}' để xóa")
        else:
            # Xóa tất cả
            all_states.clear()
            logger.info("Đã xóa tất cả trạng thái cửa sổ")

        # Cập nhật config và ghi lại file
        config['window_states'] = all_states
        save_config(config)

        return True

    except Exception as e:
        logger.error(f"Không thể xóa trạng thái cửa sổ: {e}")
        return False

# =========================
# UI UTILITY FUNCTIONS
# =========================

def setup_placeholder(entry_widget, var, placeholder):
    """
    Thêm placeholder cho Entry, tự động điều chỉnh màu sắc theo theme.
    """
    try:
        style = entry_widget.winfo_toplevel().style
        default_fg = style.colors.inputfg
        placeholder_fg = style.colors.secondary
    except (AttributeError, TclError):
        default_fg = 'black'
        placeholder_fg = 'grey'

    def on_focus_in(event):
        if var.get() == placeholder:
            var.set('')
            entry_widget.config(foreground=default_fg)
            
    def on_focus_out(event):
        if not var.get():
            var.set(placeholder)
            entry_widget.config(foreground=placeholder_fg)

    def initial_setup():
        if not entry_widget.winfo_exists():
            return
        # Cập nhật lại màu sắc phòng trường hợp theme đã thay đổi
        try:
            style = entry_widget.winfo_toplevel().style
            nonlocal default_fg, placeholder_fg
            default_fg = style.colors.inputfg
            placeholder_fg = style.colors.secondary
        except (AttributeError, TclError):
            pass # Giữ màu cũ nếu có lỗi
        
        if var.get() == placeholder or not var.get():
            var.set(placeholder)
            entry_widget.config(foreground=placeholder_fg)
        else:
            entry_widget.config(foreground=default_fg)

    entry_widget.bind('<FocusIn>', on_focus_in)
    entry_widget.bind('<FocusOut>', on_focus_out)
    entry_widget.after(PLACEHOLDER_SETUP_DELAY, initial_setup)


# *** HÀM VẼ TREEVIEW ĐÃ ĐƯỢC SỬA LỖI ***
def render_tree_from_data(tree, nodes: List[Dict[str, Any]], placeholder_text: str = ""):
    """
    Vẽ dữ liệu lên một widget Treeview từ một cấu trúc dữ liệu trung gian.
    Hàm này đã được sửa để xử lý đúng thứ tự cha-con và các trường hợp đặc biệt.

    Args:
        tree: Widget ttk.Treeview để vẽ.
        nodes: Danh sách các dict, mỗi dict đại diện cho một node.
        placeholder_text: Tin nhắn hiển thị nếu không có node nào.
    """
    try:
        tree.delete(*tree.get_children())

        if not nodes:
            if placeholder_text:
                tree.insert('', 'end', text=placeholder_text, tags=('faded',))
            return

        item_map = {'': ''}  # Maps node 'id' (string) to tkinter item ID
        
        # Sử dụng một bản sao của danh sách để loại bỏ các node khi chúng được xử lý
        remaining_nodes = list(nodes)
        
        # Lặp cho đến khi tất cả các node được xử lý hoặc không thể xử lý thêm
        last_count = -1
        max_iterations = 100  # Giới hạn số lần lặp để tránh vòng lặp vô hạn
        iteration_count = 0
        
        while remaining_nodes and len(remaining_nodes) != last_count and iteration_count < max_iterations:
            last_count = len(remaining_nodes)
            iteration_count += 1
            
            unplaced_nodes = []
            
            for node in remaining_nodes:
                parent_id_str = node.get('parent', '')
                
                # Kiểm tra xem node cha đã được vẽ lên cây chưa
                if parent_id_str in item_map:
                    parent_item_id = item_map[parent_id_str]
                    
                    node_id = node.get('id')
                    text = node.get('text', '')
                    values = node.get('values', ())
                    tags = node.get('tags', ())
                    is_open = node.get('open', False)

                    try:
                        item_id = tree.insert(parent_item_id, 'end', text=text, values=values, tags=tags, open=is_open)
                        if node_id:
                            item_map[node_id] = item_id
                    except Exception as e:
                        logger.warning(f"Lỗi khi thêm node vào tree: {e}")
                        continue
                else:
                    # Node cha chưa được tìm thấy, thêm vào danh sách để xử lý ở vòng lặp sau
                    unplaced_nodes.append(node)
            
            remaining_nodes = unplaced_nodes

        # Ghi log các node không thể đặt (node mồ côi)
        if remaining_nodes:
            for node in remaining_nodes:
                logger.warning(f"Không thể đặt node mồ côi: id='{node.get('id')}', parent='{node.get('parent')}'")
                
    except Exception as e:
        logger.error(f"Lỗi khi render tree: {e}", exc_info=True)
        tree.delete(*tree.get_children())
        tree.insert('', 'end', text=f"Lỗi hiển thị cây: {str(e)}", tags=('error',))


def insert_with_entity_and_encrypted_highlight(text_widget, text_block, style, danger_tag="encrypted", entity_tag_prefix="entity_info"):
    """Insert một khối văn bản, tự động highlight [Encrypted] và entity marker."""
    clean_block = re.sub(r'&lt;Encrypted&gt;.*?&lt;/Encrypted&gt;', '[Encrypted]', text_block, flags=re.DOTALL)
    parts = re.split(f'({re.escape("[Encrypted]")})', clean_block)
    
    for part in parts:
        if part == '[Encrypted]':
            text_widget.insert("end", part, danger_tag)
        elif part:
            _insert_with_entity_highlight(text_widget, part, style, entity_tag_prefix)

def _insert_with_entity_highlight(text_widget, s, style, entity_tag_prefix):
    """Hàm nội bộ: Chèn chuỗi có chứa marker entity, highlight và gắn sự kiện."""
    pattern = r'\[\[ENTITY\|([^\|]+)\|([\s\S]*?)\|([^\]]*)\]\]'
    last_end = 0
    tooltip_widget = None

    def show_tooltip(event, name, src):
        nonlocal tooltip_widget
        if tooltip_widget:
            tooltip_widget.destroy()
        
        try:
            current_style = event.widget.winfo_toplevel().style
            bg_color = current_style.colors.light
            fg_color = current_style.colors.fg
        except (AttributeError, TclError):
            bg_color = "#ffffe0"
            fg_color = "black"

        tooltip_widget = tb.Toplevel(text_widget)
        tooltip_widget.wm_overrideredirect(True)
        tooltip_widget.geometry(f"+{event.x_root + 15}+{event.y_root + 10}")
        msg = f"Entity: {name}"
        if src:
            msg += f"\nFile: {src}"
        label = Label(tooltip_widget, text=msg, background=bg_color, foreground=fg_color, relief="solid", borderwidth=1, justify="left")
        label.pack()

    def hide_tooltip(event):
        nonlocal tooltip_widget
        if tooltip_widget:
            tooltip_widget.destroy()
            tooltip_widget = None

    def show_file_menu(event, file_path):
        menu = Menu(None, tearoff=0)
        menu.add_command(label="Mở file", command=lambda: os.startfile(file_path))
        menu.add_command(label="Mở thư mục chứa", command=lambda: os.startfile(os.path.dirname(file_path)))
        menu.tk_popup(event.x_root, event.y_root)

    for match in re.finditer(pattern, s):
        text_widget.insert("end", s[last_end:match.start()])
        entity_name, entity_val, entity_source = match.groups()
        tag_name = f"{entity_tag_prefix}{entity_name}_{match.start()}"
        text_widget.tag_configure(tag_name, foreground=style.colors.info)
        text_widget.insert("end", entity_val, tag_name)
        text_widget.tag_bind(tag_name, "<Enter>", lambda e, n=entity_name, s_path=entity_source: show_tooltip(e, n, s_path))
        text_widget.tag_bind(tag_name, "<Leave>", hide_tooltip)
        if entity_source and os.path.exists(entity_source):
            text_widget.tag_bind(tag_name, "<Button-3>", lambda e, s_path=entity_source: show_file_menu(e, s_path))
        last_end = match.end()
    text_widget.insert("end", s[last_end:])


class StatusBar(tb.Frame):
    """Thanh trạng thái ở dưới cùng của cửa sổ."""
    def __init__(self, master, **kwargs):
        super().__init__(master, bootstyle=COLOR_LIGHT, **kwargs)
        self.status_label = tb.Label(self, text="Sẵn sàng", anchor=W, padding=(10, 5))
        self.status_label.pack(fill=X)
        self.after_id = None

    def show_message(self, msg, duration_ms=STATUS_MESSAGE_DURATION):
        self.status_label.config(text=msg)
        if self.after_id:
            self.after_cancel(self.after_id)
        self.after_id = self.after(duration_ms, self.clear_message)

    def clear_message(self):
        self.status_label.config(text="Sẵn sàng")
        self.after_id = None

class BaseWindow(tb.Window):
    """Lớp cửa sổ cơ sở chứa các thiết lập chung về style, font và chức năng đổi theme."""
    def __init__(self, title="", save_window_state=True, themename=None, **kwargs):
        current_theme = themename if themename is not None else load_theme()
        super().__init__(title=title, themename=current_theme, **kwargs)
        self.save_window_state = save_window_state
        try:
            self.scaling_factor = self.tk.call('tk', 'scaling')
        except Exception:
            self.scaling_factor = 1.0

        self.font_normal = (FONT_FAMILY, FONT_SIZE_IN_POINTS)
        self.font_bold = (FONT_FAMILY, FONT_SIZE_IN_POINTS, "bold")
        self.font_label_frame = (FONT_FAMILY, FONT_SIZE_IN_POINTS, "bold")

        self.style.configure('TLabel', font=self.font_normal)
        self.style.configure('TButton', font=self.font_normal)
        self.style.configure('TEntry', font=self.font_normal)
        self.style.configure('TLabelframe.Label', font=self.font_label_frame)
        self.style.configure('Treeview.Heading', font=self.font_bold)
        self._setup_window_state()

    def _setup_window_state(self):
        """Thiết lập và khôi phục trạng thái cửa sổ."""
        if not self.save_window_state:
            self.state('zoomed')
            return
        if not load_window_state(self):
            self.geometry(f"{DEFAULT_WINDOW_WIDTH}x{DEFAULT_WINDOW_HEIGHT}")
            self.state('zoomed')
        setup_window_state_saving(self)

    def save_state(self):
        """Lưu trạng thái cửa sổ thủ công."""
        if self.save_window_state:
            save_window_state(self)

    def set_theme(self, theme_name: str):
        """Đặt theme của ứng dụng và lưu lại lựa chọn."""
        if theme_name in THEMES_LIST:
            logger.info(f"Đang đổi theme sang: {theme_name}")
            self.style.theme_use(theme_name)
            save_theme(theme_name)
            self.update_styles_on_theme_change()
        else:
            logger.warning(f"Theme '{theme_name}' không hợp lệ.")

    def update_styles_on_theme_change(self):
        """Hàm ảo để các lớp con có thể override và cập nhật style khi theme thay đổi."""
        pass

def format_xml_string(content: str) -> str:
    """Cố gắng định dạng một chuỗi XML để dễ đọc hơn."""
    try:
        doctype_re = re.compile(r'(<!DOCTYPE\s+[^\[]+\[(.*?)\]>)', re.DOTALL)
        match = doctype_re.search(content)
        doctype_part, xml_body = "", content
        if match:
            doctype_part = match.group(0)
            xml_body = content[match.end():].strip()
        if not xml_body: return content
        dom = xml.dom.minidom.parseString(xml_body)
        pretty_xml_body = dom.toprettyxml(indent="  ")
        pretty_xml_body = '\n'.join(pretty_xml_body.split('\n')[1:]).strip()
        return f"{doctype_part}\n\n{pretty_xml_body}" if doctype_part else pretty_xml_body
    except Exception:
        return content

def highlight_text_in_widget(text_widget, content):
    """Hiển thị nội dung text thông thường trong Text widget (không có syntax highlighting)."""
    text_widget.delete('1.0', 'end')
    if content:
        text_widget.insert('1.0', content)

def center_dialog(dialog, parent=None):
    """
    Định vị dialog ở trung tâm màn hình hoặc trung tâm cửa sổ cha.
    """
    dialog.update_idletasks()
    current_geometry = dialog.geometry()
    try:
        size_part = current_geometry.split('+')[0]
        dialog_width, dialog_height = map(int, size_part.split('x'))
    except (ValueError, IndexError):
        dialog_width = dialog.winfo_reqwidth() or 400
        dialog_height = dialog.winfo_reqheight() or 300

    if parent and parent.winfo_exists():
        parent_x, parent_y = parent.winfo_x(), parent.winfo_y()
        parent_width, parent_height = parent.winfo_width(), parent.winfo_height()
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
    else:
        screen_width, screen_height = dialog.winfo_screenwidth(), dialog.winfo_screenheight()
        x = (screen_width - dialog_width) // 2
        y = (screen_height - dialog_height) // 2

    x = max(0, min(x, dialog.winfo_screenwidth() - dialog_width))
    y = max(0, min(y, dialog.winfo_screenheight() - dialog_height))
    dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
    logger.info(f"Đã định vị dialog tại: {x}+{y} (kích thước: {dialog_width}x{dialog_height})")
