# db_service.py
import pyodbc
import pandas as pd
import re
import logging
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

from constants import DB_TIMEOUT_SECONDS, ODBC_DRIVER

class MSSQLService:
    """
    Lớp Service chịu trách nhiệm cho mọi tương tác với Database.
    Đã được tái cấu trúc để loại bỏ lặp code và chuẩn hóa xử lý lỗi.
    """
    @staticmethod
    def _create_connection_string(server, db, user, pwd):
        """
        Tạo chuỗi kết nối chuẩn.
        Hỗ trợ cả SQL Authentication và Windows Authentication.
        """
        base_conn_str = f"DRIVER={{{ODBC_DRIVER}}};SERVER={server};DATABASE={db}"

        # Nếu có user và password, sử dụng SQL Authentication
        if user and user.strip():
            return f"{base_conn_str};UID={user};PWD={pwd}"
        else:
            # Sử dụng Windows Authentication (Integrated Security)
            return f"{base_conn_str};Trusted_Connection=yes"

    @staticmethod
    def _execute_query(server, db, user, pwd, query, timeout=5, fetch_type='pandas', params=None):
        """
        Hàm helper chung để kết nối, thực thi query và xử lý lỗi.
        - fetch_type: 'pandas', 'all', 'one'.
        - Trả về dữ liệu tương ứng nếu thành công.
        - Trả về None nếu có lỗi kết nối hoặc lỗi thực thi.
        """
        conn_str = MSSQLService._create_connection_string(server, db, user, pwd)
        try:
            with pyodbc.connect(conn_str, timeout=timeout, autocommit=True) as conn:
                if fetch_type == 'pandas':
                    # Sử dụng pandas với pyodbc connection một cách an toàn
                    try:
                        # Sử dụng cursor để tránh warning về pandas với pyodbc
                        cursor = conn.cursor()
                        cursor.execute(query, params if params else [])
                        columns = [column[0] for column in cursor.description]
                        data = cursor.fetchall()
                        
                        # Kiểm tra dữ liệu trước khi tạo DataFrame
                        if not data:
                            return pd.DataFrame(columns=columns)
                        
                        # Kiểm tra xem data có khớp với số cột không
                        actual_columns = len(data[0])
                        if actual_columns != len(columns):
                            logger.warning(f"Số cột không khớp: data có {actual_columns} cột, columns có {len(columns)} cột")
                            # Điều chỉnh columns để khớp với data
                            if actual_columns < len(columns):
                                columns = columns[:actual_columns]
                            else:
                                # Nếu data có nhiều cột hơn, chỉ lấy số cột cần thiết
                                data = [row[:len(columns)] for row in data]
                        
                        # Tạo DataFrame một cách an toàn bằng cách chuyển đổi dữ liệu
                        try:
                            # Chuyển đổi dữ liệu thành list of lists để tránh lỗi shape
                            data_list = []
                            for row in data:
                                # Đảm bảo mỗi row có đúng số cột
                                if len(row) == len(columns):
                                    data_list.append(list(row))
                                else:
                                    # Nếu row không đủ cột, thêm None cho các cột thiếu
                                    row_list = list(row)
                                    while len(row_list) < len(columns):
                                        row_list.append(None)
                                    data_list.append(row_list[:len(columns)])
                            
                            df = pd.DataFrame(data_list, columns=columns)
                            return df
                        except Exception as df_error:
                            logger.warning(f"Lỗi tạo DataFrame: {df_error}")
                            # Fallback: tạo DataFrame rỗng với columns đúng
                            return pd.DataFrame(columns=columns)
                    except Exception as e:
                        logger.warning(f"Lỗi khi đọc dữ liệu với pandas: {e}")
                        return pd.DataFrame()  # Trả về DataFrame rỗng nếu có lỗi
                
                cursor = conn.cursor()
                cursor.execute(query, params if params else [])
                
                if fetch_type == 'all':
                    columns = [column[0].lower() for column in cursor.description]
                    return [dict(zip(columns, row)) for row in cursor.fetchall()]
                elif fetch_type == 'one':
                    return cursor.fetchone()
                return True

        except pyodbc.Error as e:
            logger.error(f"Lỗi DB khi thực thi trên '{db}': {e}")
            return None
        except Exception as e:
            logger.error(f"Lỗi không xác định trên '{db}': {e}", exc_info=True)
            return None

    @staticmethod
    def get_menu(server, db, user, pwd):
        """
        Lấy cây menu. Trả về DataFrame, hoặc DataFrame rỗng nếu không có dữ liệu/lỗi.
        """
        query = "SELECT wmenu_id, wmenu_id0, menu_id, bar, bar2, link, parameter, sysid, type, syscode, msys, status FROM wcommand"
        result = MSSQLService._execute_query(server, db, user, pwd, query, fetch_type='pandas')
        
        # Đảm bảo trả về DataFrame hợp lệ
        if result is None or result.empty:
            # Trả về DataFrame rỗng với cấu trúc đúng
            columns = ['wmenu_id', 'wmenu_id0', 'menu_id', 'bar', 'bar2', 'link', 'parameter', 'sysid', 'type', 'syscode', 'msys', 'status']
            return pd.DataFrame(columns=columns)
        
        return result

    @staticmethod
    def get_app_databases(server, db, user, pwd):
        """
        Lấy danh sách App DB từ Sys DB. Trả về list of dicts hoặc None nếu lỗi.
        """
        query = "SELECT cdata, cname FROM entity"
        return MSSQLService._execute_query(server, db, user, pwd, query, fetch_type='all')

    @staticmethod
    def get_program_version(server, db, user, pwd):
        """
        Lấy phiên bản chương trình từ dmstt. Trả về chuỗi phiên bản hoặc None.
        """
        query = "SELECT TOP 1 version FROM dmstt"
        result = MSSQLService._execute_query(server, db, user, pwd, query, timeout=DB_TIMEOUT_SECONDS, fetch_type='one')
        return result[0] if result else None

    @staticmethod
    def _parse_sql_version(full_version_string):
        """Hàm trợ giúp để phân tích chuỗi @@VERSION thành định dạng ngắn gọn."""
        if not full_version_string:
            return ""
        try:
            product_match = re.search(r"Microsoft SQL Server \d{4}", full_version_string)
            product_part = product_match.group(0).replace("Server", "").strip() if product_match else "SQL Server"
            edition_match = re.search(r"\s{2,}([\w\s]+? Edition)", full_version_string)
            edition_part = edition_match.group(1).replace("Edition", "").replace("(64-bit)", "").strip() if edition_match else ""
            return f"{product_part} {edition_part}".strip()
        except Exception:
            return full_version_string[:40] + "..."

    @staticmethod
    def get_db_version(server, user, pwd):
        """
        Lấy phiên bản SQL Server. Trả về chuỗi đã rút gọn hoặc None.
        """
        query = "SELECT @@VERSION"
        result = MSSQLService._execute_query(server, 'master', user, pwd, query, timeout=DB_TIMEOUT_SECONDS, fetch_type='one')
        if result and result[0]:
            return MSSQLService._parse_sql_version(result[0])
        return "Không rõ" if result is not None else None

    @staticmethod
    def get_db_objects(server, db, user, pwd):
        """
        Lấy danh sách các đối tượng (Tables, Views, Stored Procedures) từ một database.
        *** ĐÃ CẬP NHẬT: Lọc bỏ các bảng phân kỳ theo năm (YYYY) và năm-tháng (YYYYMM) ***
        chỉ giữ lại các bảng gốc (ví dụ: ...$0000 hoặc ...$000000) và các bảng không phân kỳ.
        """
        conn_str = MSSQLService._create_connection_string(server, db, user, pwd)
        try:
            with pyodbc.connect(conn_str, timeout=5) as conn:
                cursor = conn.cursor()
                
                # Lấy tất cả các bảng gốc
                cursor.execute("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME")
                all_tables = [row.TABLE_NAME for row in cursor.fetchall()]
                
                # --- LOGIC LỌC BẢNG PHÂN KỲ ---
                final_tables = []
                # Regex để nhận diện các bảng có hậu tố $YYYY hoặc $YYYYMM
                partition_pattern = re.compile(r"(.+\$)(\d{4}|\d{6})$") 

                for table_name in all_tables:
                    match = partition_pattern.match(table_name)
                    
                    if not match:
                        # Nếu tên bảng không khớp mẫu phân kỳ, giữ lại nó.
                        final_tables.append(table_name)
                        continue

                    # Nếu khớp, kiểm tra xem nó có phải là bảng gốc không ($0000 hoặc $000000)
                    suffix = match.group(2)
                    if suffix in ('0000', '000000'):
                        # Đây là bảng gốc, giữ lại nó.
                        final_tables.append(table_name)
                    # Ngược lại, nó là bảng phân kỳ theo năm/tháng thực sự -> bỏ qua.

                # Lấy Views và Stored Procedures (không thay đổi)
                cursor.execute("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'VIEW' ORDER BY TABLE_NAME")
                views = [row.TABLE_NAME for row in cursor.fetchall()]
                
                cursor.execute("SELECT ROUTINE_NAME FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_TYPE = 'PROCEDURE' ORDER BY ROUTINE_NAME")
                procedures = [row.ROUTINE_NAME for row in cursor.fetchall()]
                
                return {"Tables": final_tables, "Views": views, "Stored Procedures": procedures}
                
        except pyodbc.Error as e:
            logger.error(f"Lỗi khi lấy đối tượng từ DB '{db}': {e}")
            return None
        except Exception as e:
            logger.error(f"Lỗi không xác định khi lấy đối tượng từ DB '{db}': {e}", exc_info=True)
            return None
            
    @staticmethod
    def get_db_object_definition(server, db, user, pwd, obj_name: str) -> str:
        """
        Lấy định nghĩa (mã nguồn) của một đối tượng CSDL (Stored Procedure, View, Function...).
        *** ĐỔI TÊN TỪ get_sp_definition ĐỂ CHÍNH XÁC HƠN ***
        """
        # sp_helptext an toàn hơn và là cách chuẩn để lấy định nghĩa đối tượng.
        query = "EXEC sp_helptext ?"
        try:
            # Sử dụng _execute_query để chuẩn hóa kết nối và xử lý lỗi
            result_df = MSSQLService._execute_query(
                server, db, user, pwd, query,
                params=[obj_name], fetch_type='pandas'
            )
            
            if result_df is not None and not result_df.empty:
                # Nối tất cả các dòng text lại thành một chuỗi duy nhất
                return "".join(result_df.iloc[:, 0])
            
            logger.warning(f"Không tìm thấy định nghĩa cho đối tượng '{obj_name}' trong DB '{db}'.")
            return f"-- Không tìm thấy định nghĩa cho đối tượng: {obj_name}"

        except Exception as e:
            logger.error(f"Lỗi nghiêm trọng khi lấy định nghĩa của '{obj_name}': {e}", exc_info=True)
            return f"-- Lỗi khi truy vấn định nghĩa cho đối tượng: {obj_name}\n-- Chi tiết: {e}"

    @staticmethod
    def get_table_columns(server, db, user, pwd, table_name: str) -> List[Dict[str, Any]]:
        """
        Lấy thông tin chi tiết của các cột trong một bảng.
        """
        conn_str = MSSQLService._create_connection_string(server, db, user, pwd)
        try:
            with pyodbc.connect(conn_str, timeout=5) as conn:
                # Query 1: Get all column details
                cols_query = """
                    SELECT
                        COLUMN_NAME, ORDINAL_POSITION, DATA_TYPE,
                        CHARACTER_MAXIMUM_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE,
                        IS_NULLABLE, COLUMN_DEFAULT
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = ? ORDER BY ORDINAL_POSITION;
                """
                cols_df = pd.read_sql(cols_query, conn, params=[table_name])
                if cols_df.empty:
                    return []

                # Query 2: Get primary key columns
                pk_query = """
                    SELECT kcu.COLUMN_NAME
                    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS AS tc
                    JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE AS kcu
                        ON tc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
                        AND tc.TABLE_SCHEMA = kcu.TABLE_SCHEMA
                        AND tc.TABLE_NAME = kcu.TABLE_NAME
                    WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY' AND tc.TABLE_NAME = ?;
                """
                pk_df = pd.read_sql(pk_query, conn, params=[table_name])
                pk_columns = set(pk_df['COLUMN_NAME']) if not pk_df.empty else set()

                # Merge results
                cols_df['is_primary_key'] = cols_df['COLUMN_NAME'].apply(lambda x: x in pk_columns)
                
                # Convert DataFrame to list of dicts
                return cols_df.to_dict('records')

        except (pyodbc.Error, pd.io.sql.DatabaseError) as e:
            logger.error(f"Lỗi DB khi lấy thông tin cột cho bảng '{table_name}' trong '{db}': {e}")
            return None
        except Exception as e:
            logger.error(f"Lỗi không xác định khi lấy cột cho bảng '{table_name}': {e}", exc_info=True)
            return None