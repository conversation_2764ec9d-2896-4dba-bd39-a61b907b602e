# constants.py

APP_NAME = "deAnalyst"
VERSION = "1.2.3"

# ===================================================================
# THEME & STYLING
# ===================================================================

# 1. THEME & COLORS
DEFAULT_THEME = "darkly"  # flatly, journal, litera, lumen, minty, pulse, sandstone, united, yeti, cosmo, superhero, darkly, cyborg, vapor
COLOR_PRIMARY = "primary"
COLOR_SECONDARY = "secondary"
COLOR_SUCCESS = "success"
COLOR_DANGER = "danger"
COLOR_INFO = "info"
COLOR_WARNING = "warning"
COLOR_DARK = "dark"
COLOR_LIGHT = "light"

THEMES_LIST = [
    "flatly", "journal", "litera", "lumen", "minty", "pulse",
    "sandstone", "united", "yeti", "cosmo", "superhero", "darkly",
    "cyborg", "vapor"
]

# 2. FONTS
FONT_FAMILY = "Segoe UI"
FONT_SIZE_IN_POINTS = 9

# 3. SIZING & PADDING
PANEL_PADDING = 5
FIELD_PADDING_Y = 5
FIELD_PADDING_X = 5
BUTTON_WIDTH = 15

# 4. COMPONENT STYLES
FRAME_STYLE = COLOR_SECONDARY
TOGGLE_STYLE = f"{COLOR_SECONDARY}-round-toggle"

# 5. BEHAVIOR
SEARCH_DEBOUNCE_MS = 300

# ===================================================================
# FILE & PATH CONSTANTS
# ===================================================================
SQLITE_CACHE_DIR = "cache"
CONFIG_FILE = "config.json"
LOG_DIR = "logs"
LOG_FILE_PATH = "logs/deAnalyst.log"

# ===================================================================
# LOGGING CONSTANTS
# ===================================================================
LOG_MAX_BYTES = 5 * 1024 * 1024  # 5MB
LOG_BACKUP_COUNT = 5

# ===================================================================
# FILE PARSER CONSTANTS
# ===================================================================
MAX_ENTITY_RESOLUTION_DEPTH = 20
FILE_EXTENSIONS = ('.f', '.xml', '.ent', '.txt')
LRU_CACHE_SIZE = 128 # Hằng số này vẫn còn nhưng không được sử dụng sau khi bỏ lru_cache

# ===================================================================
# EXCLUSION RULES - Các quy tắc loại trừ khi phân tích
# ===================================================================
# Cấu hình tập trung cho tất cả các exclusion rules
EXCLUSION_RULES = {
    # File extensions bị bỏ qua khi scan files và xử lý entities
    'excluded_file_extensions': ('.dll', '.exe', '.pdb', '.lib', '.obj', '.bin', '.so', '.dylib',
                                '.zip', '.rar', '.7z', '.tar', '.gz', '.pdf', '.doc', '.docx',
                                '.rpt', '.xls', '.xlsx', '.ppt', '.pptx', '.mp3', '.mp4', '.avi', '.mov',
                                '.gif', '.png', '.jpg', '.jpeg', '.bmp', '.ico'),

    # Danh sách các folder không được phân tích (relative paths từ project root)
    'excluded_folders': [
        r'\App_Data\Controllers\Structure',
        r'\App_Data\Download',
        r'\App_Data\Upload',
        r'\Upload',
    ]
}

# Backward compatibility - giữ lại các hằng số cũ
SKIP_IMAGE_ENTITY_EXTS = EXCLUSION_RULES['excluded_file_extensions']
EXCLUDED_FOLDERS = EXCLUSION_RULES['excluded_folders']
EXCLUDED_FILE_EXTENSIONS = EXCLUSION_RULES['excluded_file_extensions']

# ===================================================================
# ENCODING CONSTANTS
# ===================================================================
SUPPORTED_ENCODINGS = ['utf-8', 'utf-8-sig', 'utf-16', 'utf-16le', 'utf-16be', 'cp1252', 'latin1']

# ===================================================================
# DATABASE QUERY CONSTANTS
# ===================================================================
DB_TIMEOUT_SECONDS = 3
ODBC_DRIVER = "ODBC Driver 17 for SQL Server"

# ===================================================================
# UI TIMING CONSTANTS
# ===================================================================
STATUS_MESSAGE_DURATION = 4000  # milliseconds
PLACEHOLDER_SETUP_DELAY = 100   # milliseconds

# ===================================================================
# WINDOW STATE CONSTANTS
# ===================================================================
DEFAULT_WINDOW_WIDTH = 1200
DEFAULT_WINDOW_HEIGHT = 800
MIN_WINDOW_WIDTH = 800
MIN_WINDOW_HEIGHT = 600
