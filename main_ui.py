#!/usr/bin/env python3
"""
main_ui.py - View layer của ứng dụng deAnalyst
Chứa các UI components và xử lý giao diện người dùng
<PERSON>ân thủ mô hình MVC: Chỉ chứa View logic, không chứa business logic
"""

import os
import logging
from tkinter import messagebox, CENTER

import ttkbootstrap as tb
from ttkbootstrap.constants import *
from ttkbootstrap.scrolled import ScrolledText

from constants import *
from utils import (prepare_db_objects_tree_data, prepare_file_tree_data, 
                   prepare_menu_tree_data, prepare_usage_file_tree_data)
from utils_ui import (StatusBar, setup_placeholder, format_xml_string,
                      highlight_text_in_widget, BaseWindow, render_tree_from_data)
from project_ui import ProjectManagerUI
from code_viewer import CodeViewer
from fields_analyzer import FieldsAnalyzerUI

logger = logging.getLogger(__name__)

# ===================================================================
# VIEW CLASSES
# ===================================================================

class ProjectTopbar(tb.Frame):
    """Thanh công cụ trên cùng, chứa lựa chọn dự án và các nút hành động chính."""
    def __init__(self, master, on_select_callback, on_manage_callback, on_exit_callback, on_theme_select_callback, on_analyze_fields_callback):
        super().__init__(master)
        
        left_frame = tb.Frame(self)
        center_frame = tb.Frame(self)
        right_frame = tb.Frame(self)

        left_frame.pack(side=LEFT, padx=(0, 10))
        center_frame.pack(side=LEFT, fill=X, expand=True)
        right_frame.pack(side=RIGHT)

        tb.Label(left_frame, text="Dự án:", font=master.font_bold).pack(side=LEFT)
        
        self.combo_proj = tb.Combobox(center_frame, state="readonly", width=38)
        self.combo_proj.pack(side=LEFT, fill=X, expand=True)
        self.combo_proj.bind("<<ComboboxSelected>>", lambda e: on_select_callback())
        
        tb.Label(right_frame, text="Theme:").pack(side=LEFT, padx=(10, 5))
        self.theme_var = tb.StringVar(value=master.style.theme.name)
        theme_combo = tb.Combobox(
            right_frame,
            textvariable=self.theme_var,
            values=THEMES_LIST,
            state="readonly",
            width=12
        )
        theme_combo.pack(side=LEFT, padx=5)
        theme_combo.bind("<<ComboboxSelected>>", lambda e: on_theme_select_callback(self.theme_var.get()))
        
        tb.Button(right_frame, text="Phân tích Fields", command=on_analyze_fields_callback, bootstyle=COLOR_SUCCESS, width=BUTTON_WIDTH).pack(side=LEFT, padx=5)
        tb.Button(right_frame, text="Quản lý Dự án", command=on_manage_callback, bootstyle=COLOR_INFO, width=BUTTON_WIDTH).pack(side=LEFT, padx=5)
        tb.Button(right_frame, text="Thoát", command=on_exit_callback, bootstyle=COLOR_SECONDARY, width=BUTTON_WIDTH).pack(side=LEFT, padx=5)
        
        self.pack(side=TOP, fill=X, padx=PANEL_PADDING, pady=(PANEL_PADDING, 0))

    def load(self, projects):
        self.combo_proj['values'] = [p.get("name", "Dự án không tên") for p in projects]
        if not projects: self.combo_proj.set('')

    def set_selection(self, index):
        if 0 <= index < len(self.combo_proj['values']): self.combo_proj.current(index)

    def get_selected_index(self):
        return self.combo_proj.current()

class MenuPanel(tb.Frame):
    """Khung chứa cây menu chức năng hệ thống."""
    def __init__(self, master, on_select_callback, on_refresh_callback):
        super().__init__(master, padding=0)
        self.on_select_callback = on_select_callback
        self.on_refresh_callback = on_refresh_callback
        self.menu_df = None
        self.lang_var = tb.BooleanVar(value=False)
        self.show_id_var = tb.BooleanVar(value=False)
        self.search_var = tb.StringVar()
        self._debounce_id = None
        self.placeholder_text = "Tìm kiếm menu (tên, sysid)..."
        self._create_widgets()

    def _create_widgets(self):
        top_frame = tb.Frame(self)
        top_frame.pack(fill=X, padx=10, pady=(10, 10))
        self.search_entry = tb.Entry(top_frame, textvariable=self.search_var)
        self.search_entry.pack(side=LEFT, fill=X, expand=True, padx=(0,5))
        self.search_entry.bind('<KeyRelease>', self._on_search_debounce)
        setup_placeholder(self.search_entry, self.search_var, self.placeholder_text)
        tb.Button(top_frame, text="Làm mới", command=self.on_refresh_callback, bootstyle=f"{COLOR_INFO}-outline", width=8).pack(side=LEFT, padx=(0,10))
        tb.Checkbutton(top_frame, text="EN", variable=self.lang_var, bootstyle=TOGGLE_STYLE, command=self.update_tree_view).pack(side=LEFT)
        tb.Checkbutton(top_frame, text="ID", variable=self.show_id_var, bootstyle=TOGGLE_STYLE, command=self.update_tree_view).pack(side=LEFT, padx=(5,0))
        tree_container = tb.Frame(self)
        tree_container.pack(fill=BOTH, expand=True, padx=10, pady=(0, 10))
        self.tree = tb.Treeview(tree_container, show="tree", selectmode="browse")
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        self.tree.bind("<<TreeviewSelect>>", lambda e: self.on_select_callback())
        scrollbar = tb.Scrollbar(tree_container, orient=VERTICAL, command=self.tree.yview, bootstyle=f"{COLOR_PRIMARY}-round")
        self.tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=RIGHT, fill=Y)

    def set_data(self, df):
        self.menu_df = df
        self.search_var.set('')
        if self.search_entry: self.search_entry.event_generate('<FocusOut>')
        self.update_tree_view()

    def update_tree_view(self):
        if self.menu_df is None:
            render_tree_from_data(self.tree, [], "Đang tải...")
            return
        if self.menu_df.empty:
            render_tree_from_data(self.tree, [], "Không có dữ liệu menu.")
            return
        query = self.search_var.get().strip().lower()
        if query == self.placeholder_text.lower(): query = ""
        filtered_df = None
        if query:
            filtered_df = self.menu_df[self.menu_df.apply(
                lambda row: query in str(row.get('bar', '')).lower() or \
                            query in str(row.get('bar2', '')).lower() or \
                            query in str(row.get('sysid', '')).lower(),
                axis=1
            )]
        controller = self.winfo_toplevel().controller
        
        with controller.ui_data_lock: # Đảm bảo an toàn khi đọc
            file_info_cache = controller.dir_info_for_ui

        node_data = prepare_menu_tree_data(
            self.menu_df, filtered_df, self.lang_var.get(), self.show_id_var.get(),
            file_info_cache=file_info_cache
        )
        render_tree_from_data(self.tree, node_data)

    def _on_search_debounce(self, event=None):
        if self._debounce_id: self.after_cancel(self._debounce_id)
        self._debounce_id = self.after(SEARCH_DEBOUNCE_MS, self.update_tree_view)

    def get_selected_item_data(self):
        sel = self.tree.focus()
        if not sel: return None, None
        values = self.tree.item(sel).get("values", [])
        return (values[0], values[1]) if values and len(values) >= 2 else (None, None)
            
    def update_styles(self):
        style = self.winfo_toplevel().style
        self.tree.tag_configure('faded', foreground=style.colors.danger)
        self.tree.tag_configure('inactive', foreground=style.colors.secondary)
        self.tree.tag_configure('file_child', foreground=style.colors.info)
        self.update_tree_view()


class FileExplorerPanel(tb.Frame):
    def __init__(self, master, on_file_single_click, on_file_analyze, on_update_cache_callback, on_file_search_callback, on_reanalyze_all_callback, on_view_content_callback):
        super().__init__(master, padding=10)
        self.on_file_single_click = on_file_single_click
        self.on_file_analyze = on_file_analyze
        self.on_update_cache_callback = on_update_cache_callback
        self.on_file_search_callback = on_file_search_callback
        self.on_reanalyze_all_callback = on_reanalyze_all_callback
        self.on_view_content_callback = on_view_content_callback
        self.folder_path = None
        self._file_debounce_id = None
        self.show_errors_only_var = tb.BooleanVar(value=False)
        self._create_file_tree_widgets()
        self._create_context_menu()
        self.update_styles()

    def _create_file_tree_widgets(self):
        self.file_search_var = tb.StringVar()
        self.file_placeholder_text = "Tìm kiếm file/thư mục..."
        top_frame = tb.Frame(self)
        top_frame.pack(fill=X, pady=(0, 10))
        self.file_search_entry = tb.Entry(top_frame, textvariable=self.file_search_var)
        self.file_search_entry.pack(side=LEFT, fill=X, expand=True, padx=(0, 5))
        self.file_search_entry.bind('<KeyRelease>', self._on_file_search_debounce)
        setup_placeholder(self.file_search_entry, self.file_search_var, self.file_placeholder_text)
        self.analyze_button = tb.Button(top_frame, text="Phân tích", command=self.on_file_analyze, bootstyle=COLOR_PRIMARY)
        self.analyze_button.pack(side=LEFT, padx=(0, 5))
        self.analyze_var = tb.StringVar(value="Chức năng hàng loạt...")
        self.analyze_combo = tb.Combobox(
            top_frame, textvariable=self.analyze_var,
            values=["Phân tích các file thay đổi", "Phân tích lại tất cả file"],
            state="readonly", width=24, bootstyle=f"{COLOR_INFO}"
        )
        self.analyze_combo.pack(side=LEFT, padx=(0, 5))
        self.analyze_combo.bind("<<ComboboxSelected>>", self._on_analyze_option_selected)
        tb.Checkbutton(
            top_frame, text="Chỉ hiển thị lỗi", variable=self.show_errors_only_var,
            bootstyle="danger-square-toggle", command=self.on_file_search_callback
        ).pack(side=LEFT, padx=(10, 0))
        tree_container = tb.Frame(self)
        tree_container.pack(fill=BOTH, expand=True, pady=(5,0))
        self.file_tree = tb.Treeview(tree_container, show="tree", selectmode="browse")
        self.file_tree.pack(side=LEFT, fill=BOTH, expand=True)
        self.file_tree.bind("<<TreeviewSelect>>", lambda e: self.on_file_single_click())
        self.file_tree.bind("<Double-1>", lambda e: self.on_file_analyze())
        self.file_tree.bind("<Button-3>", self._show_file_context_menu)
        scrollbar = tb.Scrollbar(tree_container, orient=VERTICAL, command=self.file_tree.yview, bootstyle=f"{COLOR_PRIMARY}-round")
        self.file_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=RIGHT, fill=Y)

    def _create_context_menu(self):
        self.file_context_menu = tb.Menu(self, tearoff=0)
        self.file_context_menu.add_command(label="Xem (F4)", command=self.on_view_content_callback)
        self.file_context_menu.add_separator()
        self.file_context_menu.add_command(label="Mở File", command=self._open_selected_file)
        self.file_context_menu.add_command(label="Mở thư mục", command=self._open_selected_folder)
        self.file_context_menu.add_command(label="Mở thư mục chứa", command=self._open_containing_folder)
        self.file_context_menu.add_separator()
        self.file_context_menu.add_command(label="Copy đường dẫn", command=self._copy_selected_path)

    def _show_file_context_menu(self, event):
        item_id = self.file_tree.identify_row(event.y)
        if not item_id: return
        self.file_tree.selection_set(item_id)
        path, item_type = self._get_selected_item_data()
        if not path: return
        is_file = item_type == 'file'
        self.file_context_menu.entryconfig("Xem (F4)", state="normal" if is_file else "disabled")
        self.file_context_menu.entryconfig("Mở File", state="normal" if is_file else "disabled")
        self.file_context_menu.entryconfig("Mở thư mục", state="disabled" if is_file else "normal")
        self.file_context_menu.entryconfig("Mở thư mục chứa", state="normal" if is_file else "disabled")
        self.file_context_menu.entryconfig("Copy đường dẫn", state="normal")
        self.file_context_menu.post(event.x_root, event.y_root)

    def _get_selected_item_data(self):
        sel = self.file_tree.focus()
        if not sel: return None, None
        values = self.file_tree.item(sel).get("values", [])
        return (values[0], values[1]) if values else (None, None)

    def _open_selected_file(self):
        path, item_type = self._get_selected_item_data()
        if path and item_type == 'file':
            try: os.startfile(path)
            except Exception as e: messagebox.showerror("Lỗi", f"Không thể mở file:\n{e}", parent=self.winfo_toplevel())

    def _open_selected_folder(self):
        path, item_type = self._get_selected_item_data()
        if path and item_type == 'dir':
            try: os.startfile(path)
            except Exception as e: messagebox.showerror("Lỗi", f"Không thể mở thư mục:\n{e}", parent=self.winfo_toplevel())

    def _open_containing_folder(self):
        path, item_type = self._get_selected_item_data()
        if path and item_type == 'file':
            try: os.startfile(os.path.dirname(path))
            except Exception as e: messagebox.showerror("Lỗi", f"Không thể mở thư mục:\n{e}", parent=self.winfo_toplevel())

    def _copy_selected_path(self):
        path, _ = self._get_selected_item_data()
        if path:
            try:
                self.clipboard_clear()
                self.clipboard_append(os.path.normpath(os.path.abspath(path)))
                self.update() 
                self.winfo_toplevel().update_status(f"Đã copy đường dẫn: {path}", 2000)
            except Exception as e: messagebox.showerror("Lỗi", f"Không thể copy đường dẫn:\n{e}", parent=self.winfo_toplevel())

    def set_folder(self, folder_path):
        self.folder_path = folder_path
        self.file_search_var.set('')
        if self.file_search_entry: self.file_search_entry.event_generate('<FocusOut>')
        placeholder = "Đang quét nhanh file hệ thống..." if folder_path and os.path.isdir(folder_path) else \
                      f"Lỗi: Không tìm thấy thư mục:\n{folder_path}" if folder_path else "Chưa cấu hình thư mục dự án."
        render_tree_from_data(self.file_tree, [], placeholder)

    def get_open_nodes(self):
        open_nodes = set()
        def _traverse(parent_item):
            if self.file_tree.item(parent_item, 'open'):
                if item_values := self.file_tree.item(parent_item, 'values'):
                    open_nodes.add(item_values[0])
            for child in self.file_tree.get_children(parent_item): _traverse(child)
        for root_item in self.file_tree.get_children(''): _traverse(root_item)
        return open_nodes

    def _on_analyze_option_selected(self, event=None):
        selected = self.analyze_var.get()
        if "Phân tích các file thay đổi" in selected: self.on_update_cache_callback()
        elif "Phân tích lại tất cả file" in selected: self.on_reanalyze_all_callback()
        self.after(100, lambda: self.analyze_var.set("Chức năng hàng loạt..."))

    def _on_file_search_debounce(self, event=None):
        if self._file_debounce_id: self.after_cancel(self._file_debounce_id)
        self._file_debounce_id = self.after(SEARCH_DEBOUNCE_MS, self.on_file_search_callback)

    def get_selected_file_path(self):
        if not (sel := self.file_tree.focus()): return None
        if not (values := self.file_tree.item(sel).get("values", [])): return None
        return values[0] if values[1] == 'file' else None

    def update_styles(self):
        style = self.winfo_toplevel().style
        self.file_tree.tag_configure('faded', foreground=style.colors.secondary)
        self.file_tree.tag_configure('error', foreground=style.colors.danger)
        self.file_tree.tag_configure('new', foreground=style.colors.success)
        self.file_tree.tag_configure('changed', foreground=style.colors.warning)
        self.file_tree.tag_configure('cached', foreground=style.colors.secondary)

class DatabasePanel(tb.Frame):
    def __init__(self, master, on_db_select_callback, on_db_analyze_callback, on_db_search_callback, on_analyze_all_callback, on_reanalyze_all_callback, on_view_content_callback):
        super().__init__(master, padding=10)
        self.on_db_select_callback = on_db_select_callback
        self.on_db_analyze_callback = on_db_analyze_callback
        self.on_db_search_callback = on_db_search_callback
        self.on_analyze_all_callback = on_analyze_all_callback
        self.on_reanalyze_all_callback = on_reanalyze_all_callback
        self.on_view_content_callback = on_view_content_callback
        self._db_debounce_id = None
        self._create_db_objects_tree_widgets(self)
        self._create_context_menu()

    def _create_db_objects_tree_widgets(self, parent):
        top_frame = tb.Frame(parent)
        top_frame.pack(fill=X, pady=(0, 5))

        self.db_search_var = tb.StringVar()
        self.db_placeholder_text = "Lọc đối tượng DB..."
        self.db_search_entry = tb.Entry(top_frame, textvariable=self.db_search_var)
        self.db_search_entry.pack(side=LEFT, fill=X, expand=True, padx=(0, 5))
        self.db_search_entry.bind('<KeyRelease>', self._on_db_search_debounce)
        setup_placeholder(self.db_search_entry, self.db_search_var, self.db_placeholder_text)
        
        self.analyze_button = tb.Button(top_frame, text="Phân tích Cột", command=self.on_db_analyze_callback, bootstyle=COLOR_PRIMARY)
        self.analyze_button.pack(side=LEFT, padx=(0, 5))

        self.analyze_var = tb.StringVar(value="Chức năng hàng loạt...")
        self.analyze_combo = tb.Combobox(
            top_frame, textvariable=self.analyze_var,
            values=["Phân tích mục chưa có cache", "Phân tích lại tất cả mục"],
            state="readonly", width=25, bootstyle=f"{COLOR_INFO}"
        )
        self.analyze_combo.pack(side=LEFT, padx=(0, 5))
        self.analyze_combo.bind("<<ComboboxSelected>>", self._on_analyze_option_selected)

        tree_container = tb.Frame(parent)
        tree_container.pack(fill=BOTH, expand=True)
        self.db_objects_tree = tb.Treeview(tree_container, show="tree", selectmode="browse")
        self.db_objects_tree.pack(side=LEFT, fill=BOTH, expand=True)
        self.db_objects_tree.bind("<<TreeviewSelect>>", self.on_db_select_callback)
        self.db_objects_tree.bind("<Double-1>", lambda e: self.on_db_analyze_callback())
        self.db_objects_tree.bind("<Button-3>", self._show_db_context_menu)
        scrollbar = tb.Scrollbar(tree_container, orient=VERTICAL, command=self.db_objects_tree.yview, bootstyle=f"{COLOR_PRIMARY}-round")
        self.db_objects_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=RIGHT, fill=Y)

    def _create_context_menu(self):
        self.db_context_menu = tb.Menu(self, tearoff=0)
        self.db_context_menu.add_command(label="Phân tích Cột (F3)", command=self.on_db_analyze_callback)
        self.db_context_menu.add_command(label="Xem Mã nguồn (F4)", command=self.on_view_content_callback)
        self.db_context_menu.add_separator()
        self.db_context_menu.add_command(label="Copy tên", command=self._copy_selected_name)

    def _show_db_context_menu(self, event):
        item_id = self.db_objects_tree.identify_row(event.y)
        if not item_id: return
        self.db_objects_tree.selection_set(item_id)
        
        db_name, obj_type, obj_name = self.get_selected_item_data()
        if not obj_name: return
        
        can_analyze = obj_type in ["Tables", "Views"]
        can_view_code = obj_type in ["Stored Procedures", "Views"]
        
        self.db_context_menu.entryconfig("Phân tích Cột (F3)", state="normal" if can_analyze else "disabled")
        self.db_context_menu.entryconfig("Xem Mã nguồn (F4)", state="normal" if can_view_code else "disabled")
        
        self.db_context_menu.post(event.x_root, event.y_root)

    def _copy_selected_name(self):
        _, _, obj_name = self.get_selected_item_data()
        if obj_name:
            try:
                self.clipboard_clear()
                self.clipboard_append(obj_name)
                self.update()
                self.winfo_toplevel().update_status(f"Đã copy tên: {obj_name}", 2000)
            except Exception as e:
                messagebox.showerror("Lỗi", f"Không thể copy tên:\n{e}", parent=self.winfo_toplevel())

    def _on_analyze_option_selected(self, event=None):
        selected = self.analyze_var.get()
        if "Phân tích mục chưa có cache" in selected: self.on_analyze_all_callback()
        elif "Phân tích lại tất cả mục" in selected: self.on_reanalyze_all_callback()
        self.after(100, lambda: self.analyze_var.set("Chức năng hàng loạt..."))

    def get_selected_item_data(self):
        sel = self.db_objects_tree.focus()
        if not sel or '_' not in sel: return None, None, None
        
        parent = self.db_objects_tree.parent(sel)
        if not parent: return None, None, None 
        grandparent = self.db_objects_tree.parent(parent)
        if not grandparent: return None, None, None
        try:
            db_name = self.db_objects_tree.item(grandparent, 'text').strip()
            obj_type = self.db_objects_tree.item(parent, 'text').split('(')[0].strip()
            obj_name = self.db_objects_tree.item(sel, 'text').strip()
            return db_name, obj_type, obj_name
        except (IndexError, AttributeError):
            logger.warning(f"Không thể trích xuất dữ liệu từ item treeview: sel='{sel}'")
            return None, None, None

    def _on_db_search_debounce(self, event=None):
        if self._db_debounce_id: self.after_cancel(self._db_debounce_id)
        self._db_debounce_id = self.after(SEARCH_DEBOUNCE_MS, self.on_db_search_callback)

    def update_styles(self):
        if hasattr(self, 'db_objects_tree'):
            style = self.winfo_toplevel().style
            self.db_objects_tree.tag_configure('error', foreground=style.colors.danger)
            self.db_objects_tree.tag_configure('faded', foreground=style.colors.secondary)
            self.on_db_search_callback()

class AnalysisTabs(tb.Notebook):
    def __init__(self, master):
        super().__init__(master, bootstyle="primary")
        self.full_fields_data, self.full_forms_data, self.full_entities_data, self.full_categories_data = [], [], [], []
        self.tree_item_to_field_data, self.tree_item_to_form_data = {}, {}
        self.entity_search_var, self.entity_block_filter_var = tb.StringVar(), tb.StringVar(value="Tất cả")
        self._entity_debounce_id = None
        self._create_fields_context_menu()
        self.tab_entities = tb.Frame(self)
        self._create_entities_tab(self.tab_entities)
        self.add(self.tab_entities, text=" Entities ")
        self.tab_fields = tb.Frame(self)
        self._create_fields_view(self.tab_fields)
        self.add(self.tab_fields, text=" Fields ")
        self.tab_forms = tb.Frame(self)
        self._create_forms_view(self.tab_forms)
        self.add(self.tab_forms, text=" Forms ")
        self.tab_columns = tb.Frame(self)
        self._create_columns_view(self.tab_columns)
        self.add(self.tab_columns, text=" Columns ")

    def _create_entities_tab(self, parent):
        main_pane = tb.PanedWindow(parent, orient=VERTICAL)
        main_pane.pack(fill=BOTH, expand=True, padx=10, pady=10)
        top_frame = tb.Labelframe(main_pane, text="Danh sách Entity được định nghĩa", bootstyle=FRAME_STYLE, padding=5)
        main_pane.add(top_frame, weight=1)
        search_frame = tb.Frame(top_frame)
        search_frame.pack(fill=X, padx=5, pady=(0, 5))
        tb.Label(search_frame, text="Lọc tên:").pack(side=LEFT)
        search_entry = tb.Entry(search_frame, textvariable=self.entity_search_var)
        search_entry.pack(side=LEFT, fill=X, expand=True, padx=5)
        search_entry.bind('<KeyRelease>', self._on_entity_search_debounce)
        tb.Label(search_frame, text="Block:").pack(side=LEFT)
        block_filter_combo = tb.Combobox(search_frame, textvariable=self.entity_block_filter_var, values=["Tất cả", "INCLUDE", "IGNORE", "Ngoài block"], state="readonly", width=12)
        block_filter_combo.pack(side=LEFT, padx=5)
        block_filter_combo.bind("<<ComboboxSelected>>", self._on_entity_search_debounce)
        tree_container = tb.Frame(top_frame)
        tree_container.pack(fill=BOTH, expand=True)
        cols = {"entity_name": "Tên Entity", "entity_type": "Loại", "condition_state": "Trạng thái Block", "is_parameter": "Param?", "is_external": "External?", "source_file": "File Nguồn", "depth": "Mức Sâu"}
        self.entities_tree = tb.Treeview(tree_container, columns=list(cols.keys()), show="headings")
        for name, text in cols.items():
            self.entities_tree.heading(name, text=text)
            self.entities_tree.column(name, width=120, anchor=W)
        self.entities_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar = tb.Scrollbar(tree_container, orient=VERTICAL, command=self.entities_tree.yview, bootstyle=f"{COLOR_PRIMARY}-round")
        self.entities_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.entities_tree.bind("<<TreeviewSelect>>", self._on_entity_selected)
        bottom_frame = tb.Labelframe(main_pane, text="Các file sử dụng Entity đã chọn", bootstyle=FRAME_STYLE, padding=5)
        main_pane.add(bottom_frame, weight=1)
        usage_tree_container = tb.Frame(bottom_frame)
        usage_tree_container.pack(fill=BOTH, expand=True)
        self.entity_usage_tree = tb.Treeview(usage_tree_container, show="tree")
        self.entity_usage_tree.pack(side=LEFT, fill=BOTH, expand=True)
        usage_scrollbar = tb.Scrollbar(usage_tree_container, orient=VERTICAL, command=self.entity_usage_tree.yview, bootstyle=f"{COLOR_PRIMARY}-round")
        self.entity_usage_tree.configure(yscrollcommand=usage_scrollbar.set)
        usage_scrollbar.pack(side=RIGHT, fill=Y)

    def _on_entity_search_debounce(self, event=None):
        if self._entity_debounce_id: self.after_cancel(self._entity_debounce_id)
        self._entity_debounce_id = self.after(SEARCH_DEBOUNCE_MS, self._refresh_entities_tree)

    def _on_entity_selected(self, event=None):
        if not (selected_item := self.entities_tree.focus()):
            self.display_entity_usage([])
            return
        try:
            entity_name = self.entities_tree.item(selected_item, "values")[0]
            if controller := self.winfo_toplevel().controller:
                controller.select_entity(entity_name)
        except (IndexError, AttributeError):
            self.display_entity_usage([])

    def display_entity_usage(self, usage_list: list):
        controller = self.winfo_toplevel().controller
        project_root = controller.selected_project.get('f_folder', '') if controller and controller.selected_project else ''
        node_data = prepare_usage_file_tree_data(usage_list, project_root)
        render_tree_from_data(self.entity_usage_tree, node_data)

    def _refresh_entities_tree(self):
        self.entities_tree.delete(*self.entities_tree.get_children())
        self.display_entity_usage([]) 
        entities_data = self.full_entities_data or []
        query, block_filter = self.entity_search_var.get().strip().lower(), self.entity_block_filter_var.get()
        
        filtered_data = entities_data
        if query:
            filtered_data = [e for e in filtered_data if isinstance(e, dict) and query in e.get('entity_name', '').lower()]
        if block_filter != "Tất cả":
            if block_filter == "Ngoài block":
                filtered_data = [e for e in filtered_data if not e.get('condition_state')]
            else:
                filtered_data = [e for e in filtered_data if e.get('condition_state') == block_filter]

        for entity in filtered_data:
            if not isinstance(entity, dict): continue 
            values = (entity.get('entity_name', ''), entity.get('entity_type', ''), entity.get('condition_state') or "", "✔" if entity.get('is_parameter') else "", "✔" if entity.get('is_external') else "", os.path.basename(entity.get('source_file', '')), entity.get('depth', ''))
            self.entities_tree.insert('', 'end', values=values)

    def _create_forms_view(self, parent):
        main_pane = tb.PanedWindow(parent, orient=VERTICAL)
        main_pane.pack(fill=BOTH, expand=True, padx=10, pady=10)
        forms_frame = tb.Labelframe(main_pane, text="Mẫu in", bootstyle=FRAME_STYLE, padding=5)
        main_pane.add(forms_frame, weight=1)
        forms_cols = {"id": "ID", "header_v": "Header (V)", "header_e": "Header (E)", "reportFile": "Report File", "templateFile": "Template File", "commandArgument": "Argument"}
        self.forms_tree = tb.Treeview(forms_frame, columns=list(forms_cols.keys()), show="headings")
        for name, text in forms_cols.items():
            self.forms_tree.heading(name, text=text)
            self.forms_tree.column(name, width=150, anchor=W)
        self.forms_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar = tb.Scrollbar(forms_frame, orient=VERTICAL, command=self.forms_tree.yview, bootstyle=f"{COLOR_PRIMARY}-round")
        self.forms_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.forms_tree.bind("<<TreeviewSelect>>", self._on_form_selected)
        fields_frame = tb.Labelframe(main_pane, text="Chi tiết trường trong mẫu in", bootstyle=FRAME_STYLE, padding=5)
        main_pane.add(fields_frame, weight=2)
        field_cols = {"name": "Tên Field", "header_v": "Header (V)", "header_e": "Header (E)", "controller": "Controller"}
        self.form_fields_tree = tb.Treeview(fields_frame, columns=list(field_cols.keys()), show="headings")
        for name, text in field_cols.items():
            self.form_fields_tree.heading(name, text=text)
            self.form_fields_tree.column(name, width=150, anchor=W)
        self.form_fields_tree.pack(fill=BOTH, expand=True)

    def _on_form_selected(self, event=None):
        self.form_fields_tree.delete(*self.form_fields_tree.get_children())
        if not (selected_item := self.forms_tree.focus()): return
        if not (form_data := self.tree_item_to_form_data.get(selected_item)): return

        for field in form_data.get('fields', []):
            values = (field.get('name', ''), field.get('header_v', ''), field.get('header_e', ''), field.get('controller', ''))
            self.form_fields_tree.insert('', 'end', values=values)

    def _refresh_fields_tree(self):
        self.fields_tree.delete(*self.fields_tree.get_children())
        self.tree_item_to_field_data.clear()
        self._on_field_selected()
        cols_order = ['name', 'header_v', 'header_e', 'category_v', 'category_e', 'controller']
        for field in self.full_fields_data or []:
            values = tuple(field.get(k, '') for k in cols_order)
            tags = ('from_entity',) if field.get('is_from_entity') else ()
            item_id = self.fields_tree.insert('', 'end', values=values, tags=tags)
            self.tree_item_to_field_data[item_id] = field
    
    def _create_fields_view(self, parent):
        main_v_pane = tb.PanedWindow(parent, orient=VERTICAL)
        main_v_pane.pack(fill=BOTH, expand=True, padx=10, pady=10)
        top_h_pane = tb.PanedWindow(main_v_pane, orient=HORIZONTAL)
        main_v_pane.add(top_h_pane, weight=1)
        master_frame = tb.Labelframe(top_h_pane, text="Danh sách Fields", bootstyle=FRAME_STYLE, padding=5)
        top_h_pane.add(master_frame, weight=2)
        cols = {"name": "Tên Field", "header_v": "Header (V)", "header_e": "Header (E)", "category_v": "Category (V)", "category_e": "Category (E)", "controller": "Controller"}
        self.fields_tree = tb.Treeview(master_frame, columns=list(cols.keys()), show="headings")
        for name, text in cols.items():
            self.fields_tree.heading(name, text=text)
            self.fields_tree.column(name, width=150, anchor=W)
        self.fields_tree.pack(side=LEFT, fill=BOTH, expand=True)
        self.fields_tree.bind("<<TreeviewSelect>>", self._on_field_selected)
        self.fields_tree.bind("<Button-3>", self._show_fields_context_menu)
        scrollbar = tb.Scrollbar(master_frame, orient=VERTICAL, command=self.fields_tree.yview, bootstyle=f"{COLOR_PRIMARY}-round")
        self.fields_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=RIGHT, fill=Y)
        attr_frame = tb.Labelframe(top_h_pane, text="Thuộc tính File", bootstyle=FRAME_STYLE, padding=5)
        top_h_pane.add(attr_frame, weight=1)
        self.file_attr_tree = tb.Treeview(attr_frame, columns=("key", "value"), show="headings", selectmode="extended")
        self.file_attr_tree.heading("key", text="Thuộc tính")
        self.file_attr_tree.heading("value", text="Giá trị")
        self.file_attr_tree.column("key", width=150, anchor=W)
        self.file_attr_tree.column("value", width=250, anchor=W)
        self.file_attr_tree.pack(fill=BOTH, expand=True)
        detail_frame = tb.Labelframe(main_v_pane, text="Chi tiết Field", bootstyle=FRAME_STYLE, padding=5)
        main_v_pane.add(detail_frame, weight=1)
        self.detail_notebook = tb.Notebook(detail_frame)
        self.detail_notebook.pack(fill=BOTH, expand=True, padx=5, pady=5)
        tab_attr = tb.Frame(self.detail_notebook)
        self.detail_notebook.add(tab_attr, text="Thuộc tính")
        attr_pane = tb.PanedWindow(tab_attr, orient=HORIZONTAL)
        attr_pane.pack(fill=BOTH, expand=True, pady=5)
        attr_list_frame = tb.Labelframe(attr_pane, text="Thuộc tính", bootstyle=FRAME_STYLE, padding=5)
        attr_pane.add(attr_list_frame, weight=1)
        self.field_attr_tree = tb.Treeview(attr_list_frame, columns=("prop", "value"), show="headings")
        self.field_attr_tree.heading("prop", text="Thuộc tính")
        self.field_attr_tree.heading("value", text="Giá trị")
        self.field_attr_tree.column("prop", width=120)
        self.field_attr_tree.column("value", width=200)
        self.field_attr_tree.pack(fill=BOTH, expand=True)
        attr_items_frame = tb.Labelframe(attr_pane, text="Tùy chọn", bootstyle=FRAME_STYLE, padding=5)
        attr_pane.add(attr_items_frame, weight=2)
        item_cols = {"value": "Value", "text_v": "Text (V)", "text_e": "Text (E)"}
        self.field_items_tree = tb.Treeview(attr_items_frame, columns=list(item_cols.keys()), show="headings")
        for name, text in item_cols.items():
            self.field_items_tree.heading(name, text=text)
            self.field_items_tree.column(name, width=120, anchor=W)
        self.field_items_tree.pack(fill=BOTH, expand=True)
        tab_raw = tb.Frame(self.detail_notebook)
        self.detail_notebook.add(tab_raw, text="Raw XML")
        self.field_raw_xml_text = ScrolledText(tab_raw, wrap="word", relief="flat", borderwidth=0)
        self.field_raw_xml_text.pack(fill=BOTH, expand=True)
        self.field_raw_xml_text.text.config(state=DISABLED)
        self.update_styles()

    def _create_fields_context_menu(self):
        self.fields_context_menu = tb.Menu(self, tearoff=0)
        self.fields_context_menu.add_command(label="Mở File Entity", command=self._open_entity_file)
        self.fields_context_menu.add_command(label="Mở thư mục chứa File", command=self._open_entity_folder)

    def _show_fields_context_menu(self, event):
        item_id = self.fields_tree.identify_row(event.y)
        if not item_id: return
        self.fields_tree.selection_set(item_id)
        field_data = self.tree_item_to_field_data.get(item_id)
        
        source_file = field_data.get('entity_source_file') if field_data else None
        file_state = "normal" if source_file and os.path.isfile(source_file) else "disabled"
        self.fields_context_menu.entryconfig("Mở File Entity", state=file_state)
        self.fields_context_menu.entryconfig("Mở thư mục chứa File", state=file_state)
        self.fields_context_menu.post(event.x_root, event.y_root)

    def _get_selected_field_data(self):
        if item_id := self.fields_tree.focus():
            return self.tree_item_to_field_data.get(item_id)
        return None

    def _open_entity_file(self):
        if field_data := self._get_selected_field_data():
            if source_file := field_data.get('entity_source_file'):
                try: os.startfile(source_file)
                except Exception as e: messagebox.showerror("Lỗi", f"Không thể mở file:\n{e}", parent=self.winfo_toplevel())

    def _open_entity_folder(self):
        if field_data := self._get_selected_field_data():
            if source_file := field_data.get('entity_source_file'):
                try: os.startfile(os.path.dirname(source_file))
                except Exception as e: messagebox.showerror("Lỗi", f"Không thể mở thư mục:\n{e}", parent=self.winfo_toplevel())

    def _on_field_selected(self, event=None):
        field_data = self._get_selected_field_data()
        self.field_attr_tree.delete(*self.field_attr_tree.get_children())
        self.field_items_tree.delete(*self.field_items_tree.get_children())
        self.field_raw_xml_text.text.config(state=NORMAL)
        self.field_raw_xml_text.text.delete("1.0", END)
        
        if not field_data:
            self.field_raw_xml_text.text.config(state=DISABLED)
            return
            
        parsed_data = field_data.get('parsed_data', {})
        for name, parsed_value in parsed_data.get('attributes', {}).items():
            self.field_attr_tree.insert('', 'end', values=(name, parsed_value['value']))
        for prop_name in ['header_v', 'header_e', 'label', 'controller']:
            if parsed_data.get(prop_name, {}).get('value'):
                self.field_attr_tree.insert('', 'end', values=(prop_name, parsed_data[prop_name]['value']))
        for item in parsed_data.get('items_options', []):
            values = (item.get('value', ''), item.get('text_v', ''), item.get('text_e', ''))
            self.field_items_tree.insert('', 'end', values=values)
            
        raw_xml = field_data.get('raw_xml', 'Không có mã XML.')
        highlight_text_in_widget(self.field_raw_xml_text.text, format_xml_string(raw_xml))
        self.field_raw_xml_text.text.config(state=DISABLED)
        
    def _create_columns_view(self, parent):
        self.columns_label = tb.Label(parent, text="Chọn một bảng hoặc view từ cây DB Objects để xem các cột", bootstyle=SECONDARY)
        self.columns_label.pack(pady=10, padx=10, fill=X)
        cols = {"is_primary_key": "PK", "column_name": "Tên Cột", "data_type": "Kiểu Dữ liệu", "max_length": "Độ dài", "is_nullable": "Nullable"}
        self.columns_tree = tb.Treeview(parent, columns=list(cols.keys()), show="headings")
        for name, text in cols.items():
            self.columns_tree.heading(name, text=text)
            width = {"is_primary_key": 40, "column_name": 200, "data_type": 120}.get(name, 100)
            anchor = CENTER if name == "is_primary_key" else W
            self.columns_tree.column(name, width=width, anchor=anchor)

    def display_table_columns(self, columns_data):
        self.columns_label.pack_forget()
        self.columns_tree.pack(fill=BOTH, expand=True, padx=10, pady=10)
        self.columns_tree.delete(*self.columns_tree.get_children())
        if not columns_data:
            self.columns_label.config(text="Không có thông tin cột hoặc không thể tải.")
            self.columns_tree.pack_forget()
            self.columns_label.pack(pady=10, padx=10, fill=X)
            return
        for col in columns_data:
            pk_text = "PK" if col.get('is_primary_key') else ""
            data_type, length_str = (col.get('data_type', '') or '').lower(), ''
            if data_type in ['decimal', 'numeric']:
                if (p := col.get('numeric_precision')) and (s := col.get('numeric_scale')) is not None:
                    length_str = f"{int(p)},{int(s)}"
            elif 'date' not in data_type and 'time' not in data_type:
                if length := (col.get('max_length') or col.get('character_maximum_length')):
                    if int(length) > 0: length_str = str(int(length))
            nullable_text = "Có" if col.get('is_nullable') == 'YES' else "Không"
            values = (pk_text, col.get('column_name', ''), col.get('data_type', ''), length_str, nullable_text)
            self.columns_tree.insert("", "end", values=values)
        self.select(self.tab_columns)

    def prepare_for_columns_load(self, table_name):
        self.columns_label.config(text=f"Đang tải cột cho bảng: {table_name}...")
        self.columns_tree.pack_forget()
        self.columns_label.pack(pady=10, padx=10, fill=X)
        self.select(self.tab_columns)

    def clear_all(self):
        for widget in [getattr(self, w, None) for w in ['file_attr_tree', 'fields_tree', 'forms_tree', 'form_fields_tree', 'entities_tree', 'entity_usage_tree', 'field_attr_tree', 'field_items_tree', 'columns_tree']]:
            if widget: widget.delete(*widget.get_children())
        
        if hasattr(self, 'field_raw_xml_text'):
            self.field_raw_xml_text.text.config(state=NORMAL)
            self.field_raw_xml_text.text.delete("1.0", END)
            self.field_raw_xml_text.text.config(state=DISABLED)
        
        if hasattr(self, 'columns_tree'):
            self.columns_tree.pack_forget()
            self.columns_label.config(text="Chọn một bảng hoặc view từ cây DB Objects để xem các cột")
            self.columns_label.pack(pady=10, padx=10, fill=X)
            
        self.full_fields_data, self.full_forms_data, self.full_entities_data, self.full_categories_data = [], [], [], []
        self.tree_item_to_field_data, self.tree_item_to_form_data = {}, {}

    def show_data(self, fields_data=None, forms_data=None, xml_info=None, entities_data=None, categories_data=None):
        self.clear_all()
        if xml_info:
            for k, v in xml_info.items(): self.file_attr_tree.insert("", "end", values=(k, v))
        if fields_data:
            self.full_fields_data = fields_data
            self._refresh_fields_tree()
        if forms_data:
            self.full_forms_data = forms_data
            for form in forms_data:
                values = tuple(form.get(k, '') for k in ['id', 'header_v', 'header_e', 'reportFile', 'templateFile', 'commandArgument'])
                item_id = self.forms_tree.insert('', 'end', values=values)
                self.tree_item_to_form_data[item_id] = form
        if entities_data:
            self.full_entities_data = entities_data
            self._refresh_entities_tree()
        if categories_data: self.full_categories_data = categories_data
    
    def update_styles(self):
        style = self.winfo_toplevel().style
        self.fields_tree.tag_configure('from_entity', foreground=style.colors.warning)
        self._refresh_fields_tree()

class MainWindow(BaseWindow):
    def __init__(self):
        super().__init__(title=f"{APP_NAME} v{VERSION}")
        self.controller = None
        self.protocol("WM_DELETE_WINDOW", self.on_exit)

    def set_controller(self, controller):
        self.controller = controller
        logger.info("Controller đã được inject vào MainWindow")
        self._create_widgets()

    def _create_widgets(self):
        self.topbar = ProjectTopbar(self, self.on_ui_project_selected, self.open_project_manager, self.on_exit, self.set_theme, self.open_fields_analyzer)
        self.statusbar = StatusBar(self)
        self.statusbar.pack(side=BOTTOM, fill=X)
        main_pane = tb.Panedwindow(self, orient=HORIZONTAL)
        main_pane.pack(fill=BOTH, expand=1, padx=PANEL_PADDING, pady=PANEL_PADDING)
        left_v_pane = tb.Panedwindow(main_pane, orient=VERTICAL)
        main_pane.add(left_v_pane, weight=1)
        left_notebook = tb.Notebook(left_v_pane, bootstyle="primary")
        left_v_pane.add(left_notebook, weight=3) 
        self.menu_panel = MenuPanel(left_notebook, self.on_ui_menu_item_selected, self.controller.refresh_menu_data)
        left_notebook.add(self.menu_panel, text=" Đường dẫn ")
        explorer_container = tb.Frame(left_notebook, padding=0)
        left_notebook.add(explorer_container, text=" Thư mục ")
        self.explorer_panel = FileExplorerPanel(
            explorer_container, on_file_single_click=self.on_ui_show_cached_info,
            on_file_analyze=self.on_ui_file_selected, on_update_cache_callback=self.on_ui_update_cache, 
            on_file_search_callback=self.controller.filter_file_tree, on_reanalyze_all_callback=self.on_ui_reanalyze_all,
            on_view_content_callback=self.on_ui_view_file_content
        )
        self.explorer_panel.pack(fill=BOTH, expand=YES)
        database_container = tb.Frame(left_notebook, padding=0)
        left_notebook.add(database_container, text=" Cơ sở dữ liệu ")
        self.database_panel = DatabasePanel(
            database_container, self.on_ui_db_object_selected, self.on_ui_db_object_analyze,
            self.controller.filter_db_objects_tree, self.controller.start_analyze_all_db_objects,
            self.controller.start_reanalyze_all_db_objects,
            on_view_content_callback=self.on_ui_view_db_object_content
        )
        self.database_panel.pack(fill=BOTH, expand=YES)
        self.info_panel = tb.Labelframe(left_v_pane, text="Thông tin", bootstyle=FRAME_STYLE, padding=5)
        left_v_pane.add(self.info_panel, weight=1)
        self.info_tree = tb.Treeview(self.info_panel, columns=("key", "value"), show="headings", selectmode="extended")
        self.info_tree.heading("key", text="Thông tin")
        self.info_tree.heading("value", text="Giá trị")
        self.info_tree.column("key", width=150, anchor=W)
        self.info_tree.column("value", width=300, anchor=W)
        self.info_tree.pack(fill=BOTH, expand=True)
        self.analysis_tabs = AnalysisTabs(main_pane)
        main_pane.add(self.analysis_tabs, weight=3)
        
        self.bind("<F3>", self.on_f3_pressed)
        self.bind("<F4>", self.on_f4_pressed)
        self.bind("<Escape>", lambda e: self.controller.cancel_current_analysis())

    def on_f3_pressed(self, event=None):
        """Xử lý khi phím F3 được nhấn (Phân tích)."""
        focused_widget = self.focus_get()
        if hasattr(self, 'explorer_panel') and self.explorer_panel.file_tree.winfo_containing(focused_widget.winfo_rootx(), focused_widget.winfo_rooty()) == self.explorer_panel.file_tree:
            self.on_ui_file_selected()
        elif hasattr(self, 'database_panel') and self.database_panel.db_objects_tree.winfo_containing(focused_widget.winfo_rootx(), focused_widget.winfo_rooty()) == self.database_panel.db_objects_tree:
            self.on_ui_db_object_analyze()

    def on_f4_pressed(self, event=None):
        """Xử lý khi phím F4 được nhấn (Xem)."""
        focused_widget = self.focus_get()
        if hasattr(self, 'explorer_panel') and self.explorer_panel.file_tree.winfo_containing(focused_widget.winfo_rootx(), focused_widget.winfo_rooty()) == self.explorer_panel.file_tree:
            self.on_ui_view_file_content()
        elif hasattr(self, 'database_panel') and self.database_panel.db_objects_tree.winfo_containing(focused_widget.winfo_rootx(), focused_widget.winfo_rooty()) == self.database_panel.db_objects_tree:
            self.on_ui_view_db_object_content()

    def on_ui_view_file_content(self):
        if fpath := self.explorer_panel.get_selected_file_path():
            self.controller.show_code_viewer_for_file(fpath)

    def on_ui_view_db_object_content(self):
        """Yêu cầu controller mở trình xem cho SP hoặc View được chọn."""
        db_name, obj_type, obj_name = self.database_panel.get_selected_item_data()
        if obj_type in ["Stored Procedures", "Views"] and obj_name:
            self.controller.show_code_viewer_for_db_object(db_name, obj_type, obj_name)
        else:
            self.update_status("Chức năng xem chỉ hỗ trợ Stored Procedures và Views.", 3000)
    
    def open_code_viewer(self, title: str, content: str, entity_map=None, entity_source_map=None):
        try:
            CodeViewer(parent=self, controller=self.controller, title=title, content=content, entity_map=entity_map, entity_source_map=entity_source_map)
        except Exception as e:
            logger.error(f"Lỗi khi mở CodeViewer: {e}", exc_info=True)
            messagebox.showerror("Lỗi", f"Không thể mở cửa sổ xem mã:\n{e}", parent=self)

    def on_ui_project_selected(self):
        self.controller.select_project(self.topbar.get_selected_index())

    def on_ui_menu_item_selected(self):
        data, item_type = self.menu_panel.get_selected_item_data()
        if item_type == 'menu' and data: self.controller.select_menu_item(data)
        elif item_type == 'file' and data: self.on_ui_show_cached_info(file_path=data)

    def on_ui_show_cached_info(self, file_path=None):
        if fpath := file_path or self.explorer_panel.get_selected_file_path():
            self.controller.show_cached_file_info(fpath)

    def on_ui_file_selected(self, file_path=None):
        if fpath := file_path or self.explorer_panel.get_selected_file_path():
            self.controller.select_file(fpath)

    def on_ui_db_object_selected(self, event=None):
        """Callback cho click đơn: chỉ hiển thị thông tin cơ bản."""
        db_name, obj_type, obj_name = self.database_panel.get_selected_item_data()
        if obj_name:
            self.controller.select_db_object(db_name, obj_type, obj_name, analyze_columns=False)

    def on_ui_db_object_analyze(self, event=None):
        """Callback cho F3/Double-click: Phân tích cột."""
        db_name, obj_type, obj_name = self.database_panel.get_selected_item_data()
        if obj_type in ["Tables", "Views"] and obj_name:
            self.controller.select_db_object(db_name, obj_type, obj_name, analyze_columns=True)
        else:
            self.update_status("Chức năng phân tích chỉ hỗ trợ Tables và Views.", 3000)

    def on_ui_update_cache(self): self.controller.start_deep_scan_ui_trigger()
    def on_ui_reanalyze_all(self): self.controller.start_reanalyze_all_ui_trigger()

    def open_project_manager(self):
        try:
            ProjectManagerUI(parent=self).wait_window()
            self.controller.initialize_app()
        except Exception as e:
            logger.error(f"Lỗi khi mở trình quản lý dự án: {e}", exc_info=True)
            messagebox.showerror("Lỗi", f"Không thể mở trình quản lý dự án:\n{e}", parent=self)
            
    def open_fields_analyzer(self):
        try:
            FieldsAnalyzerUI(parent=self)
        except Exception as e:
            logger.error(f"Lỗi khi mở Trình Phân Tích Fields: {e}", exc_info=True)
            messagebox.showerror("Lỗi", f"Không thể mở Trình Phân Tích Fields:\n{e}", parent=self)

    def on_exit(self):
        self.controller.shutdown()
        self.save_state()
        logger.info("==================== KẾT THÚC PHIÊN LÀM VIỆC ====================")
        self.destroy()

    def update_status(self, message: str, duration_ms: int = 4000):
        self.statusbar.show_message(message, duration_ms)
    def display_projects(self, projects: list):
        self.topbar.load(projects)
        if projects: self.topbar.set_selection(0)
    def clear_all_panels(self, clear_general_info=True):
        if clear_general_info: self.info_tree.delete(*self.info_tree.get_children())
        self.analysis_tabs.clear_all()
    def prepare_for_project_load(self): self.menu_panel.set_data(None)
    def set_folder_path(self, path: str): self.explorer_panel.set_folder(path)
    def set_databases_loading(self):
        render_tree_from_data(self.database_panel.db_objects_tree, [], "Đang tải đối tượng Database...")
    def display_menu_data(self, df):
        self.menu_panel.set_data(df)
        self.menu_panel.update_styles()

    def update_file_tree(self):
        """
        FIXED: Lấy danh sách file từ cache của controller và truyền vào hàm
        prepare_file_tree_data để tránh quét lại đĩa trên luồng UI.
        """
        query = self.explorer_panel.file_search_var.get()
        show_errors_only = self.explorer_panel.show_errors_only_var.get()
        if query == self.explorer_panel.file_placeholder_text: query = ""
        
        open_nodes_before_rebuild = self.explorer_panel.get_open_nodes()
        
        # Kiểm tra folder path hợp lệ
        folder_path = self.explorer_panel.folder_path
        if not folder_path or not os.path.isdir(folder_path):
            render_tree_from_data(self.explorer_panel.file_tree, [], f"Thư mục không hợp lệ: {folder_path}")
            return
        
        with self.controller.ui_data_lock:
            all_cached_files = list(self.controller.dir_info_for_ui.keys())
            files_to_reparse = self.controller.files_to_reparse
            file_info_cache = self.controller.dir_info_for_ui

        node_data = prepare_file_tree_data(
            folder_path=folder_path,
            all_file_paths=all_cached_files,
            query=query,
            show_only_errors=show_errors_only,
            file_info_cache=file_info_cache, 
            files_to_reparse=files_to_reparse,
            previously_open_nodes=open_nodes_before_rebuild
        )
        render_tree_from_data(self.explorer_panel.file_tree, node_data, "Không có file nào khớp với bộ lọc.")

    def update_db_objects_tree(self):
        query = self.database_panel.db_search_var.get()
        if query == self.database_panel.db_placeholder_text: query = ""
        node_data = prepare_db_objects_tree_data(self.controller.db_objects_cache, query=query)
        render_tree_from_data(self.database_panel.db_objects_tree, node_data, "Chưa có dữ liệu Database")

    def display_general_info(self, info_data: dict):
        self.info_tree.delete(*self.info_tree.get_children())
        if info_data:
            for k, v in info_data.items():
                if k == 'Files liên quan' and isinstance(v, str) and '\n' in v:
                    # Hiển thị từng file trên 1 dòng, có thể click
                    for file_path in v.split('\n'):
                        if file_path.strip():
                            item_id = self.info_tree.insert("", "end", values=(k, file_path))
                            self.info_tree.item(item_id, tags=("file_link",))
                else:
                    self.info_tree.insert("", "end", values=(k, v))
        else:
            self.info_tree.insert("", "end", values=("Thông tin", "Không có dữ liệu để hiển thị."))
        # Gắn sự kiện click cho file_link
        self.info_tree.tag_bind("file_link", "<Double-1>", self._on_info_file_click)

    def _on_info_file_click(self, event):
        item = self.info_tree.identify_row(event.y)
        if not item:
            return
        values = self.info_tree.item(item, "values")
        if values and len(values) == 2 and values[0] == "Files liên quan":
            file_path = values[1]
            self.controller.show_cached_file_info(file_path)

    def display_file_analysis_result(self, file_path: str, info: dict):
        status_info = {"Đường dẫn": os.path.normpath(file_path)}
        if info.get("dir_info", {}).get("id"): status_info["Mã chứng từ"] = info["dir_info"]["id"]
        if info.get("dir_info", {}).get("error"): status_info["Lỗi"] = info["dir_info"]["error"]
        self.display_general_info(status_info)
        self.analysis_tabs.show_data(
            fields_data=info.get("fields", []),
            forms_data=info.get("forms", []),
            entities_data=info.get("entities", []),
            categories_data=info.get("categories", []),
            xml_info=info.get("dir_info", {})
        )

    def display_file_analysis_error(self, error_msg: str):
        self.display_general_info({"Lỗi phân tích": error_msg})
        self.analysis_tabs.clear_all()
    
    def set_analysis_buttons_state(self, enabled: bool):
        button_state, combo_state = ("normal", "readonly") if enabled else ("disabled", "disabled")
        try:
            if self.winfo_exists():
                self.explorer_panel.analyze_button.config(state=button_state)
                self.explorer_panel.analyze_combo.config(state=combo_state)
                self.database_panel.analyze_button.config(state=button_state)
                self.database_panel.analyze_combo.config(state=combo_state)
        except Exception as e: logger.warning(f"Không thể cập nhật trạng thái nút phân tích: {e}")

    def update_styles_on_theme_change(self):
        super().update_styles_on_theme_change()
        setup_placeholder(self.menu_panel.search_entry, self.menu_panel.search_var, self.menu_panel.placeholder_text)
        self.menu_panel.update_styles()
        setup_placeholder(self.explorer_panel.file_search_entry, self.explorer_panel.file_search_var, self.explorer_panel.file_placeholder_text)
        self.explorer_panel.update_styles()
        setup_placeholder(self.database_panel.db_search_entry, self.database_panel.db_search_var, self.database_panel.db_placeholder_text)
        self.database_panel.update_styles()
        self.analysis_tabs.update_styles()
        if self.controller.current_file_path: self.controller.select_file(self.controller.current_file_path)
