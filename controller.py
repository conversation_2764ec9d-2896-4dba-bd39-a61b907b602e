# controller.py
import os
import threading
import concurrent.futures
from tkinter import messagebox

from code_viewer import CodeViewer
from sqlite_service import ProjectService
from mssql_service import MSSQLService
from file_parser import FileParser
from sqlite_service import ProjectDataService
import logging
logger = logging.getLogger(__name__)

# Sử dụng tất cả các lõi CPU có sẵn để tối đa hóa hiệu suất phân tích
MAX_WORKERS = os.cpu_count() or 4

class MainController:
    """
    Lớp điều khiển chính của <PERSON>ng dụng, chịu trách nhiệm điều phối luồng dữ liệu
    giữa View (giao diện) và các lớp Service (logic nghiệp vụ).
    """
    def __init__(self, view):
        self.view = view
        self.is_running = True
        self.projects = []
        self.selected_project = None
        self.menu_df = None

        self.sqlite_service = None
        self.project_service = ProjectService()
        self.dir_info_for_ui = {}
        self.files_to_reparse = []
        self.current_file_path = None

        self.db_objects_cache = {}
        self.current_attr_filter = None
        
        self.analysis_lock = threading.Lock()
        self.ui_data_lock = threading.Lock()
        self.stop_analysis_flag = threading.Event()

    def shutdown(self):
        """
        Được gọi khi ứng dụng đóng để dừng các tiến trình nền.
        """
        logger.info("Nhận được tín hiệu shutdown. Đang dừng các tác vụ nền...")
        self.is_running = False
        self.cancel_current_analysis()

    def _schedule_on_view(self, callable_func, *args, **kwargs):
        """
        Lên lịch một hàm để gọi trên luồng UI chính một cách an toàn.
        """
        if self.is_running:
            try:
                self.view.after(0, lambda: callable_func(*args, **kwargs))
            except Exception as e:
                logger.warning(f"Lỗi khi lên lịch tác vụ UI (có thể do đang tắt app): {e}")

    def _run_batch_task(self, items_to_process: list, worker_func: callable, post_process_func: callable, task_name_for_ui: str):
        """
        Hàm chung để chạy các tác vụ song song, thu thập kết quả,
        sau đó xử lý kết quả một cách tuần tự.
        """
        if not self.analysis_lock.acquire(blocking=False):
            self._schedule_on_view(self.view.update_status, "Một tiến trình phân tích khác đang chạy.", 4000)
            return

        self.stop_analysis_flag.clear()
        self.view.set_analysis_buttons_state(False)

        def task_wrapper():
            try:
                total_items = len(items_to_process)
                results = []
                
                self._schedule_on_view(self.view.update_status, f"Bắt đầu {task_name_for_ui} {total_items} mục...", 5000)
                
                with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
                    future_to_item = {executor.submit(worker_func, item): item for item in items_to_process}
                    
                    count = 0
                    for future in concurrent.futures.as_completed(future_to_item):
                        if self.stop_analysis_flag.is_set():
                            for f in future_to_item: f.cancel()
                            break
                        
                        count += 1
                        try:
                            if result := future.result():
                                results.append(result)
                        except Exception as exc:
                            logger.error(f'Worker cho mục {future_to_item[future]} gây ra lỗi: {exc}', exc_info=True)

                        status_msg = f"(Esc để dừng) Đang {task_name_for_ui} {count}/{total_items}..."
                        if count % 10 == 0 or count == total_items:
                             self._schedule_on_view(self.view.update_status, status_msg, 5000)

                if self.stop_analysis_flag.is_set():
                    self._schedule_on_view(self.view.update_status, f"Đã dừng {task_name_for_ui}. Đã xử lý {len(results)} mục.", 4000)
                    return

                self._schedule_on_view(self.view.update_status, f"Đã xử lý xong. Đang lưu {len(results)} kết quả vào cache...", 10000)
                post_process_func(results)

                final_status = f"Hoàn tất. Đã {task_name_for_ui} và lưu cache {len(results)} mục."
                self._schedule_on_view(self.view.update_status, final_status, 8000)
                self._schedule_on_view(self.view.update_file_tree)

            finally:
                self._schedule_on_view(self.view.set_analysis_buttons_state, True)
                self.analysis_lock.release()
        
        # Bắt đầu luồng thực thi tác vụ
        threading.Thread(target=task_wrapper, daemon=True).start()


    def _run_file_analysis(self, files_to_process: list, task_name: str):
        """
        FIXED: Hoàn thiện hàm, gọi _run_batch_task để thực thi.
        Chạy tác vụ phân tích file.
        """
        def worker(file_path):
            if self.stop_analysis_flag.is_set(): return None
            # Trả về một tuple (đường dẫn, kết quả phân tích)
            return (file_path, FileParser.extract_entity_info(file_path))

        def post_processor(results):
            # `results` bây giờ là một list các tuple (file_path, info)
            if self.sqlite_service:
                # Giả sử bulk_update_cache_from_parser được thiết kế để xử lý định dạng này
                self.sqlite_service.bulk_update_cache_from_parser(results)

            with self.ui_data_lock:
                for file_path, info in results:
                    self.dir_info_for_ui[file_path] = info.get("dir_info", {})
                    if file_path in self.files_to_reparse:
                        self.files_to_reparse.remove(file_path)
            
            self._schedule_on_view(self.view.update_status, f"Đã lưu cache cho {len(results)} file.", 5000)

        # Gọi hàm thực thi tác vụ hàng loạt
        self._run_batch_task(files_to_process, worker, post_processor, task_name)


    def _run_db_analysis(self, objects_to_process: list, task_name: str):
        """Chạy tác vụ phân tích CSDL (lấy thông tin cột)."""
        
        def worker(db_object):
            db_name, table_name = db_object
            if self.stop_analysis_flag.is_set(): return None
            
            db_info = next((db for db in self.selected_project.get("databases", []) if db.get("name") == db_name), None)
            if not db_info: return None
            
            columns = MSSQLService.get_table_columns(server=db_info["server"], db=db_info["name"], user=db_info["user"], pwd=db_info["pass"], table_name=table_name)
            return (db_name, table_name, columns)

        def post_processor(results):
            if self.sqlite_service:
                self.sqlite_service.bulk_save_table_columns(results)
            self._schedule_on_view(self.view.update_status, f"Đã lưu cache CSDL cho {len(results)} đối tượng.", 5000)

        self._run_batch_task(objects_to_process, worker, post_processor, task_name)

    def initialize_app(self):
        self.view.update_status("Đang tải danh sách dự án...", 3000)
        self.projects = self.project_service.load_projects()
        self.view.display_projects(self.projects)
        if self.projects:
            self.select_project(0)
        else:
            self.view.update_status("Không tìm thấy dự án nào. Vui lòng tạo mới.", 5000)

    def select_project(self, project_index: int):
        if not (0 <= project_index < len(self.projects)):
            self.selected_project = None
            self.view.clear_all_panels()
            return
        
        self.selected_project = self.projects[project_index]
        p = self.selected_project
        if not (project_name := p.get("name")):
            messagebox.showerror("Lỗi", "Dự án không có tên, không thể tạo cache.")
            return

        self.sqlite_service = ProjectDataService(project_name)
        self.current_file_path = None
        self.view.update_status(f"Đang tải dữ liệu cho dự án '{p.get('name')}'...", 10000)
        self.view.clear_all_panels()
        self.view.prepare_for_project_load()
        self._load_menu()
        
        if folder_path := p.get("f_folder"):
            if not folder_path.strip():
                self.view.update_status("Thư mục dự án chưa được cấu hình.", 5000)
                return
            self.view.set_folder_path(folder_path)
            if os.path.isdir(folder_path):
                threading.Thread(target=self._run_fast_scan, args=(folder_path,), daemon=True).start()
            else:
                self.view.update_status(f"Thư mục dự án không tồn tại: {folder_path}", 5000)
        
        self.view.set_databases_loading()
        if databases := p.get("databases", []):
            threading.Thread(target=self._build_db_objects_cache, args=(databases,), daemon=True).start()
        
        self._load_entities_from_database()
        
    def start_deep_scan_ui_trigger(self):
        if not self.files_to_reparse:
            self.view.update_status("Không có file nào cần cập nhật. Cache đã là mới nhất.", 4000)
            return
        files_to_process = list(self.files_to_reparse)
        self._run_file_analysis(files_to_process, "phân tích file thay đổi")

    def start_reanalyze_all_ui_trigger(self):
        """
        FIXED: Chuyển tác vụ quét file sang luồng nền để tránh treo UI.
        Bắt đầu quá trình chuẩn bị để phân tích lại tất cả các file.
        """
        folder_path = self.selected_project.get("f_folder")
        if not folder_path or not os.path.isdir(folder_path):
            self.view.update_status("Thư mục dự án không hợp lệ.", 3000)
            return

        self.view.update_status("Đang quét danh sách file, vui lòng đợi...", 10000)
        threading.Thread(target=self._background_prepare_reanalyze, args=(folder_path,), daemon=True).start()

    def _background_prepare_reanalyze(self, folder_path: str):
        """
        NEW: Hàm chạy trên luồng nền để quét file và sau đó yêu cầu xác nhận trên luồng UI.
        """
        try:
            all_files_dict = self.sqlite_service.get_all_files_in_project(folder_path)
            all_files_list = list(all_files_dict.keys())
            # Lên lịch để hỏi người dùng trên luồng UI
            self._schedule_on_view(self._ask_and_run_reanalyze, all_files_list)
        except Exception as e:
            logger.error(f"Lỗi khi quét file để phân tích lại: {e}", exc_info=True)
            self._schedule_on_view(self.view.update_status, "Lỗi khi quét danh sách file!", 5000)

    def _ask_and_run_reanalyze(self, all_files: list):
        """
        NEW: Hàm chạy trên luồng UI để hiển thị hộp thoại xác nhận.
        """
        if not all_files:
            self.view.update_status("Không tìm thấy file nào để phân tích.", 3000)
            return
            
        if messagebox.askyesno("Xác nhận", f"Bạn có chắc muốn phân tích lại tất cả {len(all_files)} files?"):
            self._run_file_analysis(all_files, "phân tích lại tất cả file")
        else:
            self.view.update_status("Đã hủy tác vụ phân tích lại.", 3000)


    def start_analyze_all_db_objects(self):
        objects_to_process = []
        for db_name, objects in self.db_objects_cache.items():
            if objects:
                cached_tables = self.sqlite_service.get_all_cached_tables_and_views(db_name)
                objects_to_process.extend([(db_name, table) for table in objects.get("Tables", []) if table not in cached_tables])
                objects_to_process.extend([(db_name, view) for view in objects.get("Views", []) if view not in cached_tables])
        if not objects_to_process:
            self.view.update_status("Tất cả các bảng/view đã có trong cache.", 3000)
            return
        self._run_db_analysis(objects_to_process, "phân tích CSDL chưa có cache")

    def start_reanalyze_all_db_objects(self):
        objects_to_process = []
        for db_name, objects in self.db_objects_cache.items():
            if objects:
                objects_to_process.extend([(db_name, table) for table in objects.get("Tables", [])])
                objects_to_process.extend([(db_name, view) for view in objects.get("Views", [])])
        if not objects_to_process:
            self.view.update_status("Không có đối tượng DB nào để phân tích.", 3000)
            return
        if messagebox.askyesno("Xác nhận", f"Bạn có chắc muốn phân tích lại tất cả {len(objects_to_process)} bảng/view?"):
            self.sqlite_service.clear_all_table_columns_cache()
            self._run_db_analysis(objects_to_process, "phân tích lại tất cả CSDL")
            
    def cancel_current_analysis(self):
        if not self.analysis_lock.locked(): return
        logger.info("Yêu cầu dừng tác vụ phân tích...")
        self._schedule_on_view(self.view.update_status, "Đang yêu cầu dừng, vui lòng đợi...", 3000)
        self.stop_analysis_flag.set()

    def _run_fast_scan(self, folder_path: str):
        if not self.sqlite_service: return
        
        # Kiểm tra folder path hợp lệ trước khi bắt đầu quét
        if not os.path.isdir(folder_path):
            self._schedule_on_view(self.view.update_status, f"Thư mục không tồn tại: {folder_path}", 5000)
            return
            
        try:
            self._schedule_on_view(self.view.update_status, "Đang quét và so sánh file...", 10000)
            if not self.is_running: return

            disk_files_dict = self.sqlite_service.get_all_files_in_project(folder_path)
            files_to_reparse = self.sqlite_service.get_files_to_reparse(disk_files_dict)
            
            self._schedule_on_view(self.view.update_status, "Đang tải dữ liệu từ cache...", 5000)
            if not self.is_running: return
            
            # Tải thông tin metadata từ cache
            cached_dir_info = self.sqlite_service.get_all_dir_info()
            
            with self.ui_data_lock:
                self.files_to_reparse = files_to_reparse
                
                # Bắt đầu với thông tin từ cache
                final_ui_info = cached_dir_info.copy()
                
                # Đảm bảo tất cả file từ đĩa đều có trong danh sách UI
                for file_path in disk_files_dict.keys():
                    if file_path not in final_ui_info:
                        final_ui_info[file_path] = {}
                
                # Đảm bảo thư mục gốc cũng có trong danh sách để cây hiển thị đúng
                root_path = os.path.normpath(folder_path)
                if root_path not in final_ui_info:
                    final_ui_info[root_path] = {}

                self.dir_info_for_ui = final_ui_info
            
            self._schedule_on_view(self._on_fast_scan_complete, len(self.dir_info_for_ui))

        except Exception as e:
            logger.error(f"Lỗi trong quá trình quét nhanh: {e}", exc_info=True)
            self._schedule_on_view(self.view.update_status, "Lỗi khi quét file!", 5000)

    def _on_fast_scan_complete(self, count: int):
        self.view.update_file_tree()
        if self.menu_df is not None:
            self.view.display_menu_data(self.menu_df)
        status_msg = f"Hiển thị {count} files từ cache. "
        status_msg += f"Phát hiện {len(self.files_to_reparse)} file mới/thay đổi." if self.files_to_reparse else "Cache đã được cập nhật."
        self.view.update_status(status_msg, 10000)

    def show_cached_file_info(self, file_path: str):
        if not file_path or not os.path.isfile(file_path): return
        self.current_file_path = file_path
        info_data = {"Đường dẫn": os.path.normpath(file_path)}
        
        with self.ui_data_lock:
            cached_info = self.dir_info_for_ui.get(file_path, {})
            
        if cached_info.get("id"): info_data["Mã chứng từ"] = cached_info["id"]
        if cached_info.get("error"): info_data["Lỗi"] = cached_info["error"]
        
        self._schedule_on_view(self.view.display_general_info, info_data)
        self._schedule_on_view(self.view.update_status, f"Đã chọn file: {os.path.basename(file_path)}")

    def select_file(self, file_path: str):
        if not file_path or not os.path.isfile(file_path): return
        self.view.set_analysis_buttons_state(False)
        self.current_file_path = file_path
        def task():
            info = self._get_analysis_info(file_path)
            def _update_ui():
                self.view.display_file_analysis_result(file_path, info)
                success = not info.get("analysis_failed", True)
                status_msg = f"Phân tích thành công: {os.path.basename(file_path)}" if success else f"Không thể phân tích: {os.path.basename(file_path)}"
                self.view.update_status(status_msg, 5000)
                self.view.update_file_tree()
                self.view.set_analysis_buttons_state(True)
            self._schedule_on_view(_update_ui)
        threading.Thread(target=task, daemon=True).start()

    def _analyze_and_update_single_file(self, file_path: str) -> bool:
        try:
            info = FileParser.extract_entity_info(file_path)
            with self.ui_data_lock:
                self.dir_info_for_ui[file_path] = info.get("dir_info", {})
                if file_path in self.files_to_reparse:
                    self.files_to_reparse.remove(file_path)
            if self.sqlite_service:
                self.sqlite_service.update_cache_from_parser(file_path, info)
            return not info.get("analysis_failed", True)
        except Exception as e:
            logger.error(f"Lỗi nghiêm trọng khi phân tích file: {file_path}", exc_info=True)
            with self.ui_data_lock:
                 self.dir_info_for_ui[file_path] = {"error": str(e)}
            return False

    def _get_analysis_info(self, file_path: str) -> dict:
        if not self.sqlite_service:
            return FileParser.extract_entity_info(file_path)
        try:
            os.path.getmtime(file_path)
        except FileNotFoundError:
            return {"analysis_failed": True, "dir_info": {"error": "File không tồn tại."}}
        
        with self.ui_data_lock:
            is_reparse_needed = file_path in self.files_to_reparse
            
        if is_reparse_needed:
            self._analyze_and_update_single_file(file_path)
            
        return self.sqlite_service.get_analysis_info_from_cache(file_path)

    def select_entity(self, entity_name: str):
        if not self.sqlite_service or not entity_name:
            self._schedule_on_view(self.view.analysis_tabs.display_entity_usage, [])
            return
        def _fetch_usage():
            if not self.is_running: return
            usage_list = self.sqlite_service.get_entity_usage(entity_name)
            self._schedule_on_view(self.view.analysis_tabs.display_entity_usage, usage_list)
        threading.Thread(target=_fetch_usage, daemon=True).start()

    def _load_entities_from_database(self):
        if not self.sqlite_service: return
        def _fetch_entities():
            if not self.is_running: return
            try:
                if entities_data := self.sqlite_service.get_entity_structure():
                    self._schedule_on_view(self.view.analysis_tabs.show_data, entities_data=entities_data)
            except Exception as e:
                logger.error(f"Lỗi khi tải entities từ database: {e}")
        threading.Thread(target=_fetch_entities, daemon=True).start()

    def _load_menu(self):
        if not self.sqlite_service: return
        def _load_from_cache_and_refresh():
            if not (cached_menu := self.sqlite_service.load_menu_from_cache()).empty:
                self._schedule_on_view(self._on_menu_loaded, cached_menu, self.selected_project.get('name'), True)
            self._trigger_background_menu_refresh()
        threading.Thread(target=_load_from_cache_and_refresh, daemon=True).start()

    def _trigger_background_menu_refresh(self):
        if not self.selected_project: return
        sys_db_info = self._get_sys_db_info(self.selected_project)
        if not sys_db_info:
            if self.menu_df is None or self.menu_df.empty:
                 self._schedule_on_view(self.view.display_menu_data, None)
                 self._schedule_on_view(self.view.update_status, "Chưa cấu hình Sys DB để tải menu.", 5000)
            return
        if not self.is_running: return
        latest_menu_df = MSSQLService.get_menu(**sys_db_info)
        if latest_menu_df is not None and not latest_menu_df.empty:
            if self.sqlite_service:
                self.sqlite_service.save_menu_to_cache(latest_menu_df)
            self._schedule_on_view(self._on_menu_loaded, latest_menu_df, self.selected_project.get('name'), False)
        elif self.menu_df is None:
            self._schedule_on_view(self.view.update_status, "Lỗi: Không thể tải menu từ CSDL.", 5000)
            
    def _on_menu_loaded(self, menu_df, project_name, is_from_cache):
        self.menu_df = menu_df
        self.view.display_menu_data(self.menu_df)
        status = f"Hiển thị menu cho '{project_name}' từ cache. Đang làm mới..." if is_from_cache else f"Menu cho dự án '{project_name}' đã được làm mới."
        self.view.update_status(status)

    def refresh_menu_data(self):
        self.view.update_status("Đang làm mới menu từ CSDL...", 5000)
        threading.Thread(target=self._trigger_background_menu_refresh, daemon=True).start()

    def _get_sys_db_info(self, project_data: dict) -> dict | None:
        """
        Lấy thông tin Sys DB và chỉ trả về các key cần thiết để tránh lỗi TypeError.
        """
        sys_db = next((db for db in project_data.get('databases', []) if db.get('type') == 'Sys'), None)
        if sys_db:
            return {
                "server": sys_db.get("server"),
                "db": sys_db.get("name"),
                "user": sys_db.get("user"),
                "pwd": sys_db.get("pass")
            }
        return None

    def _build_db_objects_cache(self, databases: list):
        self.db_objects_cache.clear()
        for db in databases:
            if not self.is_running: return
            if not (db_name := db.get('name')): continue
            try:
                objects = MSSQLService.get_db_objects(server=db.get("server"), db=db_name, user=db.get("user"), pwd=db.get("pass"))
                self.db_objects_cache[db_name] = objects
            except Exception:
                logger.error(f"Lỗi không mong muốn khi lấy đối tượng từ DB: {db_name}", exc_info=True)
                self.db_objects_cache[db_name] = None
        self._schedule_on_view(self._on_db_objects_cache_built)

    def _on_db_objects_cache_built(self):
        self.view.update_status("Tải xong danh sách đối tượng Database.", 4000)
        self.view.update_db_objects_tree()

    def select_menu_item(self, wmenu_id: str):
        if not wmenu_id or self.menu_df is None:
            self._schedule_on_view(self.view.display_general_info, {})
            return
        row = self.menu_df[self.menu_df['wmenu_id'] == wmenu_id]
        if row.empty:
            self._schedule_on_view(self.view.display_general_info, {})
            return
        r = row.iloc[0]
        status = r.get('status', 'N/A')
        status_text = "Hoạt động" if status == '1' else "Không hoạt động" if status == '0' else status
        info_data = {
            "Tên Menu (V)": r.get('bar', 'N/A'), "Tên Menu (E)": r.get('bar2', 'N/A'), "SysID": r.get('sysid', 'N/A'),
            "SysCode": r.get('syscode', 'N/A'), "Loại": r.get('type', 'N/A'), "Trạng thái": status_text,
            "Web Menu ID": r.get('wmenu_id', 'N/A'), "Web Menu ID Cha": r.get('wmenu_id0', 'N/A'), "Menu ID": r.get('menu_id', 'N/A'),
        }

        # Tìm file liên quan theo syscode hoặc menu_id
        related_files = []
        syscode = r.get('syscode')
        menu_id = r.get('menu_id')
        with self.ui_data_lock:
            for file_path, meta in self.dir_info_for_ui.items():
                if meta.get('syscode') == syscode or meta.get('menu_id') == menu_id:
                    related_files.append(file_path)
        info_data['Files liên quan'] = '\n'.join(related_files) if related_files else 'Không có file liên quan'

        self._schedule_on_view(self.view.display_general_info, info_data)
        self._schedule_on_view(self.view.update_status, f"Đã chọn menu: {r.get('bar', 'N/A')}")

    def select_db_object(self, db_name: str, obj_type: str, obj_name: str, analyze_columns: bool = False):
        if not obj_name: return
        
        info_data = {"Database": db_name, "Loại đối tượng": obj_type, "Tên đối tượng": obj_name}
        self._schedule_on_view(self.view.display_general_info, info_data)
        self._schedule_on_view(self.view.update_status, f"Đã chọn đối tượng DB: {obj_name}")
        
        if analyze_columns and obj_type in ["Tables", "Views"]:
            self._schedule_on_view(self.view.prepare_for_columns_load, obj_name)
            threading.Thread(target=self._load_table_columns, args=(db_name, obj_name), daemon=True).start()
        else:
            self._schedule_on_view(self.view.display_table_columns, [])

    def _load_table_columns(self, db_name: str, table_name: str):
        if not self.sqlite_service: return
        if cached_columns := self.sqlite_service.get_table_columns(db_name, table_name):
            self._schedule_on_view(self.view.display_table_columns, cached_columns)
            return
        self._schedule_on_view(self.view.update_status, f"Đang tải cột cho bảng {table_name}...", 5000)
        db_info = next((db for db in self.selected_project.get("databases", []) if db.get("name") == db_name), None)
        if not db_info:
            self._schedule_on_view(self.view.display_table_columns, [])
            return
        columns = MSSQLService.get_table_columns(server=db_info.get("server"), db=db_info.get("name"), user=db_info.get("user"), pwd=db_info.get("pass"), table_name=table_name)
        if columns is not None:
            self.sqlite_service.save_table_columns(db_name, table_name, columns)
            self._schedule_on_view(self.view.display_table_columns, columns)
        else:
            self._schedule_on_view(self.view.display_table_columns, [])

    def filter_file_tree(self):
        self.view.update_file_tree()
        
    def filter_db_objects_tree(self):
        self.view.update_db_objects_tree()

    def show_code_viewer_for_file(self, file_path: str):
        if not file_path or not os.path.isfile(file_path):
            messagebox.showwarning("Lỗi File", f"Không tìm thấy file:\n{file_path}", parent=self.view)
            return
        self._schedule_on_view(self.view.update_status, f"Đang mở file: {os.path.basename(file_path)}...")
        def task():
            try:
                info = self._get_analysis_info(file_path)
                self._schedule_on_view(self.view.open_code_viewer, f"File: {os.path.basename(file_path)}", info.get("original_content", ""), info.get("entity_map", {}), info.get("entity_source_map", {}))
                self._schedule_on_view(self.view.update_status, f"Đã mở file: {os.path.basename(file_path)}", 3000)
            except Exception as e:
                self._schedule_on_view(messagebox.showerror, "Lỗi", f"Không thể mở file:\n{e}", parent=self.view)
        threading.Thread(target=task, daemon=True).start()

    def show_code_viewer_for_db_object(self, db_name: str, obj_type: str, obj_name: str):
        """Mở CodeViewer cho một đối tượng CSDL (SP hoặc View)."""
        if not self.selected_project: return
        self._schedule_on_view(self.view.update_status, f"Đang tải định nghĩa: {obj_name}...")

        def task():
            try:
                db_info = next((db for db in self.selected_project.get("databases", []) if db.get("name") == db_name), None)
                if not db_info:
                    raise ValueError(f"Không tìm thấy thông tin kết nối cho DB '{db_name}'")
                
                content = MSSQLService.get_db_object_definition(server=db_info.get("server"), db=db_info.get("name"), user=db_info.get("user"), pwd=db_info.get("pass"), obj_name=obj_name)
                
                title = f"{obj_type}: {obj_name} (DB: {db_name})"
                self._schedule_on_view(self.view.open_code_viewer, title, content)
                self._schedule_on_view(self.view.update_status, f"Đã mở: {obj_name}", 3000)

            except Exception as e:
                logger.error(f"Lỗi khi tải định nghĩa {obj_name}: {e}", exc_info=True)
                self._schedule_on_view(messagebox.showerror, "Lỗi", f"Không thể tải định nghĩa:\n{e}", parent=self.view)

        threading.Thread(target=task, daemon=True).start()