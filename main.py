#!/usr/bin/env python3
"""
main.py - Entry point của ứng dụng deAnalyst
Tuân thủ mô hình MVC: Khởi tạo và kết nối Model-View-Controller
"""

import os
import sys
import ctypes
import logging
import traceback
from logging.handlers import RotatingFileHandler
from tkinter import messagebox

from constants import *
from controller import MainController
from main_ui import MainWindow

# ===================================================================
# APPLICATION CONFIGURATION
# ===================================================================

def setup_application_logging():
    """
    Cấu hình hệ thống logging tập trung cho ứng dụng.
    - Ghi ra file với cơ chế xoay vòng (RotatingFileHandler).
    - <PERSON><PERSON>n thị trên console với định dạng đơn giản hơn.
    """
    # Tạo thư mục log nếu chưa có
    if not os.path.exists(LOG_DIR):
        os.makedirs(LOG_DIR)

    # 1. Lấy logger gốc
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)  # Bắt tất cả các level từ DEBUG trở lên

    # 2. Xóa các handler đã có để tránh log trùng lặp
    if logger.hasHandlers():
        logger.handlers.clear()

    # 3. Tạo File Handler
    # Ghi log vào file, mỗi file tối đa 5MB, giữ lại 5 file cũ nhất.
    file_handler = RotatingFileHandler(
        LOG_FILE_PATH, maxBytes=LOG_MAX_BYTES, backupCount=LOG_BACKUP_COUNT, encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)  # Chỉ ghi INFO và các level cao hơn vào file
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s (%(filename)s:%(lineno)d)'
    )
    file_handler.setFormatter(file_formatter)

    # 4. Tạo Console Handler
    # Hiển thị log trên console với định dạng đơn giản hơn
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)  # Hiển thị tất cả level trên console
    console_formatter = logging.Formatter('%(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)

    # 5. Thêm các handler vào logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    # 6. Thiết lập global exception handler
    sys.excepthook = global_exception_handler

    return logger

def global_exception_handler(exc_type, exc_value, exc_traceback):
    """
    Xử lý các exception không được bắt (uncaught exceptions).
    Ghi log chi tiết và hiển thị thông báo lỗi cho người dùng.
    """
    # Bỏ qua KeyboardInterrupt (Ctrl+C)
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    # Ghi log chi tiết
    tb_lines = traceback.format_exception(exc_type, exc_value, exc_traceback)
    error_log_message = "".join(tb_lines)

    logger = logging.getLogger(__name__)
    logger.critical(f"UNCAUGHT EXCEPTION:\n{error_log_message}")

    # Hiển thị thông báo lỗi cho người dùng
    user_message = (
        "Rất tiếc, một lỗi không mong muốn đã xảy ra.\n\n"
        "Ứng dụng có thể cần phải khởi động lại. "
        "Vui lòng kiểm tra file 'logs/deAnalyst.log' để biết thêm chi tiết."
    )

    try:
        messagebox.showerror("Lỗi Hệ Thống", user_message)
    except:
        # Nếu không thể hiển thị messagebox, in ra console
        print(f"CRITICAL ERROR: {user_message}")
        print(f"Details: {error_log_message}")

def validate_application_environment():
    """
    Kiểm tra môi trường chạy ứng dụng.

    Returns:
        bool: True nếu môi trường hợp lệ
    """
    try:
        # Kiểm tra quyền ghi trong thư mục hiện tại
        test_file = "test_write_permission.tmp"
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)

        # Kiểm tra thư mục logs có thể tạo được không
        if not os.path.exists(LOG_DIR):
            os.makedirs(LOG_DIR)

        return True

    except Exception as e:
        print(f"Lỗi kiểm tra môi trường: {e}")
        return False

def setup_application_directories():
    """Tạo các thư mục cần thiết cho ứng dụng."""
    directories = [LOG_DIR, SQLITE_CACHE_DIR]

    for directory in directories:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory)
                logging.info(f"Đã tạo thư mục: {directory}")
            except Exception as e:
                logging.error(f"Không thể tạo thư mục {directory}: {e}")
                raise

def initialize_application_config():
    """
    Khởi tạo toàn bộ cấu hình ứng dụng.
    Gọi function này trước khi khởi động ứng dụng.
    """
    # 1. Kiểm tra môi trường
    if not validate_application_environment():
        raise RuntimeError("Môi trường chạy ứng dụng không hợp lệ")

    # 2. Tạo các thư mục cần thiết
    setup_application_directories()

    # 3. Setup logging
    logger = setup_application_logging()

    # 4. Log thông tin khởi động
    logger.info(f"Khởi động {APP_NAME} v{VERSION}")
    logger.info(f"Log file: {LOG_FILE_PATH}")

    return logger

def setup_dpi_awareness():
    """Kích hoạt DPI Awareness cho Windows."""
    try:
        if sys.platform == "win32":
            ctypes.windll.shcore.SetProcessDpiAwareness(2)
    except Exception as e:
        print(f"Lỗi khi cài đặt DPI Awareness: {e}")

def create_application():
    """
    Tạo ứng dụng theo mô hình MVC.

    Returns:
        tuple: (view, controller) - View và Controller instances
    """
    # 1. Setup application configuration (Model layer service)
    logger = initialize_application_config()
    logger.info("==================== BẮT ĐẦU PHIÊN LÀM VIỆC MỚI ====================")

    # 2. Tạo View
    view = MainWindow()

    # 3. Tạo Controller và inject View dependency
    controller = MainController(view)

    # 4. Inject Controller vào View (Dependency Injection)
    view.set_controller(controller)

    # 5. Khởi tạo ứng dụng
    controller.initialize_app()

    return view, controller

def main():
    """Main function - Entry point của ứng dụng."""
    setup_dpi_awareness()

    try:
        view, controller = create_application()
        view.mainloop()
    except Exception as e:
        logging.critical(f"Lỗi nghiêm trọng khi khởi động ứng dụng: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
