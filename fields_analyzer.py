# fields_analyzer.py
"""
Ứng dụng tổng hợp:
1. <PERSON><PERSON><PERSON> <PERSON><PERSON> đồ họa (GUI) để xem danh sách các trường và tùy chọn
   của chúng dưới dạng cây thư mục tương tác.
2. <PERSON><PERSON><PERSON> hợp chức năng xuất dữ liệu đang hiển thị ra file báo cáo CSV.
***
Phiên bản mới nhất:
- T<PERSON><PERSON> cấu trúc thành Toplevel để tích hợp vào ứng dụng chính.
- T<PERSON><PERSON> chọn (option) sẽ hiển thị nội dung vào 2 cột Tiêu đề (V) và (E).
"""
import os
import sys
import threading
import csv
from collections import defaultdict
from tkinter import filedialog

import ttkbootstrap as tb
from ttkbootstrap.constants import *

from sqlite_service import ProjectService, ProjectDataService
from file_parser import FileParser
from utils_ui import center_dialog

class FieldsAnalyzerUI(tb.Toplevel):
    """Cửa sổ giao diện cho chức năng Phân tích Fields."""
    def __init__(self, parent):
        super().__init__(parent)
        self.title("Trình Phân Tích Fields (GUI & Export)")
        self.geometry("1200x800")
        self.transient(parent)
        center_dialog(self, parent)

        self.project_service = ProjectService()
        self.projects = []
        self.loaded_data = None
        self.doc_id_map = None
        self._create_widgets()
        self.load_projects_into_combobox()
        
        self.grab_set() # Khóa tương tác với cửa sổ chính

    def _create_widgets(self):
        """Tạo các thành phần giao diện chính."""
        main_frame = tb.Frame(self, padding=10)
        main_frame.pack(fill=BOTH, expand=YES)

        # --- Top Frame for controls ---
        top_frame = tb.Frame(main_frame)
        top_frame.pack(fill=X, pady=(0, 10))
        tb.Label(top_frame, text="Chọn dự án:", font=("-weight bold")).pack(side=LEFT, padx=(0, 5))
        self.project_combo = tb.Combobox(top_frame, state="readonly", width=40)
        self.project_combo.pack(side=LEFT, fill=X, expand=YES)
        self.load_button = tb.Button(top_frame, text="Tải dữ liệu", command=self.on_load_button_click, bootstyle=SUCCESS)
        self.load_button.pack(side=LEFT, padx=10)
        self.export_button = tb.Button(top_frame, text="Export ra CSV", command=self.on_export_button_click, bootstyle=INFO, state=DISABLED)
        self.export_button.pack(side=LEFT, padx=(0, 10))

        # --- Treeview Frame ---
        tree_frame = tb.Frame(main_frame)
        tree_frame.pack(fill=BOTH, expand=YES)
        columns = {"#0": "Mã CT / Category / Trường / Giá trị tùy chọn", "header_v": "Tiêu đề / Tên tùy chọn (V)", "header_e": "Tiêu đề / Tên tùy chọn (E)"}
        self.tree = tb.Treeview(tree_frame, columns=list(columns.keys())[1:], show="tree headings")
        
        for col, heading in columns.items():
            self.tree.heading(col, text=heading)
        
        self.tree.column("#0", width=400, stretch=True)
        self.tree.column("header_v", width=350, stretch=True)
        self.tree.column("header_e", width=350, stretch=True)

        self.tree.pack(side=LEFT, fill=BOTH, expand=YES)
        scrollbar = tb.Scrollbar(tree_frame, orient=VERTICAL, command=self.tree.yview, bootstyle="round")
        self.tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=RIGHT, fill=Y)

        # --- Status Bar ---
        self.status_label = tb.Label(main_frame, text="Sẵn sàng.", anchor=W, bootstyle=SECONDARY)
        self.status_label.pack(side=BOTTOM, fill=X, pady=(10, 0))

    def load_projects_into_combobox(self):
        """Tải danh sách dự án và đưa vào combobox."""
        self.projects = self.project_service.load_projects()
        if self.projects:
            self.project_combo['values'] = [p.get("name") for p in self.projects]
            self.project_combo.current(0)
        else:
            self.status_label.config(text="Không tìm thấy dự án nào.")
            self.load_button.config(state=DISABLED)

    def on_load_button_click(self):
        """Xử lý sự kiện khi nhấn nút Tải dữ liệu."""
        if self.project_combo.current() < 0: return
        project_name = self.project_combo.get()
        self.tree.delete(*self.tree.get_children())
        self.status_label.config(text=f"Đang đọc cache cho dự án '{project_name}'...")
        self.load_button.config(state=DISABLED)
        self.export_button.config(state=DISABLED)
        thread = threading.Thread(target=self._process_project_data, args=(project_name,), daemon=True)
        thread.start()

    def _process_project_data(self, project_name: str):
        """Chạy trong luồng nền để quét, phân tích và nhóm dữ liệu."""
        try:
            pds = ProjectDataService(project_name)
            files_to_process = pds.get_files_with_document_id()
            
            if not files_to_process:
                self.after(0, lambda: self.status_label.config(text="Cache trống hoặc không có file nào chứa mã chứng từ."))
                self.after(0, lambda: self.load_button.config(state=NORMAL))
                return

            docs_data = defaultdict(lambda: defaultdict(lambda: defaultdict(dict)))
            doc_id_to_filename = {}
            total_files = len(files_to_process)

            for i, (file_path, doc_id) in enumerate(files_to_process.items()):
                status_update = f"Đang phân tích file {i+1}/{total_files}: {os.path.basename(file_path)}"
                self.after(0, lambda text=status_update: self.status_label.config(text=text))
                
                if not os.path.exists(file_path): continue

                result = FileParser.extract_entity_info(file_path)
                if result.get("analysis_failed"): continue
                
                doc_id_to_filename[doc_id] = os.path.basename(file_path)

                for field in result.get("fields", []):
                    field_name = field.get("name")
                    if field_name:
                        category_name = field.get('category_v') or "(Chung)"
                        docs_data[doc_id][category_name][field_name] = {
                            'header_v': field.get('header_v', ''),
                            'header_e': field.get('header_e', ''),
                            'options': field.get('parsed_data', {}).get('items_options', [])
                        }
            
            self.loaded_data = dict(docs_data)
            self.doc_id_map = doc_id_to_filename
            self.after(0, self._update_treeview)

        except Exception as e:
            error_message = f"Lỗi: {e}"
            self.after(0, lambda: self.status_label.config(text=error_message))
            self.after(0, lambda: self.load_button.config(state=NORMAL))

    def _update_treeview(self):
        """Chạy trên luồng GUI chính để cập nhật cây dữ liệu."""
        self.tree.delete(*self.tree.get_children())
        if not self.loaded_data:
            self.status_label.config(text="Hoàn tất. Không tìm thấy chứng từ nào hợp lệ.")
            self.load_button.config(state=NORMAL)
            self.export_button.config(state=DISABLED)
            return

        for doc_id, categories in sorted(self.loaded_data.items()):
            doc_node = self.tree.insert("", END, text=f"📄 {doc_id}", open=False)

            for category_name, fields in sorted(categories.items()):
                category_node = self.tree.insert(doc_node, END, text=f"📂 {category_name}", open=True)
                
                for field_name, field_info in sorted(fields.items()):
                    field_node = self.tree.insert(
                        category_node, END,
                        text=f"    → {field_name}",
                        values=(field_info.get('header_v', ''), field_info.get('header_e', '')),
                        open=False
                    )
                    
                    options = field_info.get('options', [])
                    if options:
                        for option in options:
                            option_text = f"        • {option.get('value', 'N/A')}"
                            option_v = option.get('text_v', '')
                            option_e = option.get('text_e', '')
                            
                            self.tree.insert(field_node, END, text=option_text, values=(option_v, option_e))

        self.status_label.config(text=f"Hoàn tất. Đã tải dữ liệu cho {len(self.loaded_data)} chứng từ.")
        self.load_button.config(state=NORMAL)
        self.export_button.config(state=NORMAL)

    def on_export_button_click(self):
        """Xử lý sự kiện nhấn nút Export, mở hộp thoại lưu file và ghi dữ liệu."""
        if not self.loaded_data:
            self.status_label.config(text="Không có dữ liệu để xuất.")
            return

        file_path = filedialog.asksaveasfilename(defaultextension=".csv", filetypes=[("CSV UTF-8", "*.csv"), ("All Files", "*.*")], title="Lưu báo cáo Fields", initialfile="bao_cao_fields.csv")
        if not file_path: return

        try:
            headers = ["document_file", "document_id", "category_name", "field_name", "field_header_v", "field_header_e", "option_value", "option_text_v", "option_text_e"]
            
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=headers)
                writer.writeheader()
                for doc_id, categories in sorted(self.loaded_data.items()):
                    for category_name, fields in sorted(categories.items()):
                        for field_name, field_info in sorted(fields.items()):
                            base_row = {
                                "document_file": self.doc_id_map.get(doc_id, ""),
                                "document_id": doc_id,
                                "category_name": category_name,
                                "field_name": field_name,
                                "field_header_v": field_info.get('header_v', ''),
                                "field_header_e": field_info.get('header_e', '')
                            }
                            options = field_info.get('options', [])
                            if options:
                                for option in options:
                                    row = base_row.copy()
                                    row.update({"option_value": option.get('value', ''), "option_text_v": option.get('text_v', ''), "option_text_e": option.get('text_e', '')})
                                    writer.writerow(row)
                            else:
                                writer.writerow(base_row)
            
            self.status_label.config(text=f"Xuất báo cáo thành công: {os.path.basename(file_path)}")
        except IOError as e:
            self.status_label.config(text=f"Lỗi khi ghi file: {e}")
