# code_viewer.py
# Phiên bản sử dụng thanh tiêu đề chuẩn của Windows, tập trung vào sự ổn định và hiệu năng.

import os
import re
import logging
from tkinter import TclError, messagebox
from typing import List, Tuple, Dict, Any, Optional

import ttkbootstrap as tb
from ttkbootstrap.constants import *

from utils_ui import (StatusBar, center_dialog, format_xml_string, 
                      setup_placeholder, load_config, save_config)
from constants import (FONT_FAMILY, FONT_SIZE_IN_POINTS, FRAME_STYLE, 
                       SEARCH_DEBOUNCE_MS, COLOR_PRIMARY, COLOR_INFO)

logger = logging.getLogger(__name__)

# =============================================================================
# LỚP CỬA SỔ XEM MÃ CHÍNH (CODE VIEWER)
# =============================================================================
class CodeViewer(tb.Toplevel):
    """
    Cửa sổ hiển thị nội dung mã nguồn, sử dụng thanh tiêu đề chuẩn của hệ điều hành.
    """
    def __init__(self, parent, controller, title: str, content: str, 
                 entity_map: Optional[Dict[str, Any]] = None, 
                 entity_source_map: Optional[Dict[str, Any]] = None):
        super().__init__(parent)

        # --- Khởi tạo thuộc tính ---
        self.controller = controller
        self.entity_map = entity_map or {}
        self.entity_source_map = entity_source_map or {}
        
        self.search_results_indices: List[Tuple[str, str]] = []
        self.current_search_index = -1
        self.is_fullscreen_mode = False

        # --- Cấu hình cửa sổ ---
        self._setup_window(title, parent)
        self._create_widgets()
        self.display_content(content)
        
        self.focus_set()

    def _setup_window(self, title: str, parent):
        """Cấu hình các thuộc tính cơ bản và sự kiện của cửa sổ."""
        self.title(title)
        
        # Tải trạng thái (kích thước, vị trí) đã lưu, nếu không có thì dùng mặc định
        if not self._load_state():
            self.geometry("900x700")
        
        self.protocol("WM_DELETE_WINDOW", self._on_close) # Gọi hàm _on_close khi đóng
        self.bind("<F11>", self.toggle_fullscreen)
        self.bind("<Escape>", self.exit_fullscreen)

        # Căn giữa cửa sổ so với cửa sổ cha
        self.after(10, lambda: center_dialog(self, parent))
        
        # Cho phép cửa sổ con tồn tại độc lập, không khóa cửa sổ chính
        self.transient(parent)

    def _create_widgets(self):
        """Tạo và sắp xếp các widget trên cửa sổ."""
        self._create_toolbar()
        self.statusbar = StatusBar(self)
        self.statusbar.pack(side=BOTTOM, fill=X)
        self._create_text_widget()

    def _create_toolbar(self):
        """Tạo thanh công cụ chứa các nút chức năng."""
        tool_bar = tb.Frame(self, padding=(10, 10, 10, 0))
        tool_bar.pack(side=TOP, fill=X)
        
        self.search_var = tb.StringVar()
        search_entry = tb.Entry(tool_bar, textvariable=self.search_var)
        search_entry.pack(side=LEFT, fill=X, expand=True, padx=(0, 5))
        search_entry.bind("<Return>", self._find_next)
        search_entry.bind("<KeyRelease>", self._on_search_debounce)
        setup_placeholder(search_entry, self.search_var, "Nhập để tìm kiếm...")

        self.find_button = tb.Button(tool_bar, text="Tìm", command=self._find_next, bootstyle=COLOR_PRIMARY)
        self.find_button.pack(side=LEFT, padx=(0, 5))
        
        self.search_status_label = tb.Label(tool_bar, text="")
        self.search_status_label.pack(side=LEFT, padx=(0, 10))
        
        self.fullscreen_button = tb.Button(tool_bar, text="Toàn màn hình (F11)", command=self.toggle_fullscreen, bootstyle=f"{COLOR_INFO}-outline")
        self.fullscreen_button.pack(side=RIGHT)

    def _create_text_widget(self):
        """Tạo widget ScrolledText và cấu hình các tag."""
        container = tb.Frame(self, bootstyle=FRAME_STYLE, padding=1)
        container.pack(fill=BOTH, expand=True, padx=10, pady=10)

        font_to_use = (FONT_FAMILY, FONT_SIZE_IN_POINTS)
        self.text_widget = tb.ScrolledText(container, font=font_to_use, relief="flat", borderwidth=0, wrap="none")
        self.text_widget.pack(fill=BOTH, expand=True)

        # Cấu hình các tag để highlight
        try:
            self.text_widget.tag_configure("entity_link", foreground=self.style.colors.info, underline=True)
            self.text_widget.tag_configure("search_highlight", background=self.style.colors.warning)
            self.text_widget.tag_configure("current_highlight", background=self.style.colors.success)
        except (AttributeError, TclError):
            logger.error("Lỗi khi cấu hình tag cho Text widget.")
        
        self.text_widget.tag_bind("entity_link", "<Enter>", lambda e: self.config(cursor="hand2"))
        self.text_widget.tag_bind("entity_link", "<Leave>", lambda e: self.config(cursor=""))

    # --- Các hàm điều khiển cửa sổ ---
    def toggle_fullscreen(self, event=None):
        self.is_fullscreen_mode = not self.is_fullscreen_mode
        self.attributes("-fullscreen", self.is_fullscreen_mode)
        self.fullscreen_button.config(text="Thoát (F11/Esc)" if self.is_fullscreen_mode else "Toàn màn hình (F11)")

    def exit_fullscreen(self, event=None):
        if self.is_fullscreen_mode:
            self.toggle_fullscreen()
    
    def _on_close(self):
        """Lưu trạng thái trước khi đóng cửa sổ."""
        self._save_state()
        self.destroy()

    # --- Các hàm lưu và tải trạng thái cửa sổ ---
    def _save_state(self):
        """Lưu trạng thái (kích thước, vị trí) của cửa sổ vào file config."""
        try:
            config = load_config()
            if 'window_states' not in config:
                config['window_states'] = {}
            
            # Chỉ lưu geometry nếu không ở chế độ fullscreen
            if not self.is_fullscreen_mode:
                geometry = self.geometry()
                config['window_states']['CodeViewer'] = {'geometry': geometry}
                save_config(config)
        except Exception as e:
            logger.error(f"Không thể lưu trạng thái cửa sổ CodeViewer: {e}")

    def _load_state(self) -> bool:
        """Tải và áp dụng trạng thái cửa sổ từ file config."""
        try:
            config = load_config()
            state_data = config.get('window_states', {}).get('CodeViewer')
            
            if state_data and 'geometry' in state_data:
                self.geometry(state_data['geometry'])
                return True
            return False
        except Exception as e:
            logger.error(f"Không thể tải trạng thái cửa sổ CodeViewer: {e}")
            return False

    # --- Các hàm xử lý nội dung và sự kiện ---
    def display_content(self, content: str):
        """Hiển thị nội dung một cách hiệu quả, tối ưu cho file lớn."""
        self.text_widget.config(state=NORMAL)
        self.text_widget.delete("1.0", END)

        formatted_content = content
        if len(content) < 1_000_000: # Giới hạn 1MB để tránh treo
            try: formatted_content = format_xml_string(content)
            except Exception: pass

        parts: List[Tuple[str, tuple]] = []
        entity_ref_pattern = re.compile(r'&([\w\.\-%]+);')
        last_end = 0

        for match in entity_ref_pattern.finditer(formatted_content):
            parts.append((formatted_content[last_end:match.start()], ()))
            entity_name = match.group(1)
            entity_text = match.group(0)
            if entity_name in self.entity_source_map:
                tag_name = f"entity_{entity_name}_{match.start()}"
                parts.append((entity_text, ("entity_link", tag_name)))
                self.text_widget.tag_bind(tag_name, "<Button-1>", lambda e, en=entity_name: self._on_entity_click(en))
            else:
                parts.append((entity_text, ()))
            last_end = match.end()
        
        parts.append((formatted_content[last_end:], ()))

        for text, tags in parts:
            self.text_widget.insert(END, text, tags)

        self.text_widget.config(state=DISABLED)
        self.statusbar.show_message(f"Đã tải xong: {self.title()}", 10000)

    def _on_entity_click(self, entity_name: str):
        source_path = self.entity_source_map.get(entity_name)
        if source_path and os.path.isfile(source_path):
            self.controller.show_code_viewer_for_file(source_path)
        else:
            messagebox.showinfo("Thông báo", f"Không tìm thấy file nguồn cho entity '{entity_name}'.\nĐường dẫn: {source_path}", parent=self)

    # --- Logic tìm kiếm ---
    def _on_search_debounce(self, event=None):
        if hasattr(self, '_search_debounce_id'):
            self.after_cancel(self._search_debounce_id)
        self._search_debounce_id = self.after(SEARCH_DEBOUNCE_MS, self._perform_search)

    def _perform_search(self):
        self.text_widget.tag_remove("search_highlight", "1.0", END)
        self.text_widget.tag_remove("current_highlight", "1.0", END)
        self.search_results_indices = []
        self.current_search_index = -1
        query = self.search_var.get()
        if not query or query == "Nhập để tìm kiếm...":
            self.search_status_label.config(text="")
            return
        
        start_pos = "1.0"
        while True:
            start_pos = self.text_widget.search(query, start_pos, stopindex=END, nocase=True)
            if not start_pos: break
            end_pos = f"{start_pos}+{len(query)}c"
            self.search_results_indices.append((start_pos, end_pos))
            self.text_widget.tag_add("search_highlight", start_pos, end_pos)
            start_pos = end_pos
            
        if self.search_results_indices:
            self.search_status_label.config(text=f"Tìm thấy: {len(self.search_results_indices)}")
            self._find_next()
        else:
            self.search_status_label.config(text="Không tìm thấy")

    def _find_next(self, event=None):
        if not self.search_results_indices:
            self._perform_search()
            return
            
        if self.current_search_index != -1:
            start, end = self.search_results_indices[self.current_search_index]
            self.text_widget.tag_remove("current_highlight", start, end)
            self.text_widget.tag_add("search_highlight", start, end)

        self.current_search_index = (self.current_search_index + 1) % len(self.search_results_indices)
        
        start, end = self.search_results_indices[self.current_search_index]
        self.text_widget.tag_remove("search_highlight", start, end)
        self.text_widget.tag_add("current_highlight", start, end)
        self.text_widget.see(start)
        
        self.search_status_label.config(text=f"Kết quả {self.current_search_index + 1}/{len(self.search_results_indices)}")
