# utils.py
import os
from typing import List, Dict, Any
from collections import defaultdict
from constants import FILE_EXTENSIONS

# ===================================================================
# DATA PREPARATION FUNCTIONS (LOGIC)
# ===================================================================

def prepare_menu_tree_data(full_df, filtered_df=None, lang_is_en=False, show_id=False, file_info_cache=None) -> List[Dict[str, Any]]:
    """
    Chuẩn bị dữ liệu cây menu dưới dạng cấu trúc trung gian để hiển thị.
    """
    if full_df is None or full_df.empty:
        return []

    files_by_main_id = defaultdict(list)
    if file_info_cache:
        for path, info in file_info_cache.items():
            # Thử nhiều trường có thể chứa ID để match với syscode của menu
            # Ưu tiên: syscode -> sysid -> id -> menu_id (để match với menu syscode)
            main_id = info.get('syscode') or info.get('sysid') or info.get('id') or info.get('menu_id')
            if main_id:
                files_by_main_id[main_id.strip().lower()].append(path)

    nodes = []
    id_map = {row['wmenu_id']: row.to_dict() for _, row in full_df.iterrows()}
    
    is_filtered = filtered_df is not None and not filtered_df.empty
    
    display_ids = set(id_map.keys())
    if is_filtered:
        display_ids = set(filtered_df['wmenu_id'])
        for wmenu_id in list(display_ids):
            current = id_map.get(wmenu_id)
            while current:
                if parent_id := current.get('wmenu_id0'):
                    if parent_id in display_ids: break
                    display_ids.add(parent_id)
                    current = id_map.get(parent_id)
                else:
                    break
    
    parent_map = defaultdict(list)
    for wmenu_id in display_ids:
        row = id_map[wmenu_id]
        parent_map[row['wmenu_id0']].append(row)

    root_ids = set(parent_map.keys()) - set(id_map.keys())

    def add_nodes_recursively(parent_id_in_data):
        children = sorted(parent_map.get(parent_id_in_data, []), key=lambda x: x.get('wmenu_id', ''))
        for row in children:
            name = (row.get('bar2') or row['bar']) if lang_is_en else row['bar']
            if show_id: name = f"{name} ({row['wmenu_id']})"

            status = row.get('status', '').strip()
            tags = ['inactive'] if status == '0' else []
            
            node_parent_id = parent_id_in_data if parent_id_in_data not in root_ids else ''
            menu_node_id = row['wmenu_id']
            nodes.append({
                'id': menu_node_id, 'parent': node_parent_id, 'text': name,
                'values': (row['wmenu_id'], 'menu'), 'tags': tuple(tags), 'open': is_filtered
            })
            
            if menu_syscode := row.get('syscode'):
                # Tìm các file có ID match với syscode của menu
                for file_path in sorted(files_by_main_id.get(menu_syscode.strip().lower(), [])):
                    nodes.append({
                        'id': f"file_child_{menu_node_id}_{file_path}", 'parent': menu_node_id,
                        'text': os.path.basename(file_path), 'values': (file_path, 'file'),
                        'tags': ('file_child',), 'open': False
                    })
            add_nodes_recursively(row['wmenu_id'])

    for root_id in sorted(list(root_ids)):
        add_nodes_recursively(root_id)
        
    return nodes

def prepare_file_tree_data(folder_path: str, all_file_paths: List[str], query: str = "", 
                           show_only_errors: bool = False, file_info_cache: Dict = None, 
                           files_to_reparse: List[str] = None, 
                           previously_open_nodes: set = None) -> List[Dict[str, Any]]:
    """
    FIXED: Cải thiện thuật toán xây dựng cây thư mục để xử lý tốt hơn các trường hợp đặc biệt.
    Hàm này xây dựng cây thư mục trong bộ nhớ một cách hiệu quả và chính xác.
    """
    if not folder_path or not all_file_paths:
        return []

    root_path = os.path.normpath(folder_path)
    query = query.strip().lower()
    file_info_cache = file_info_cache or {}
    files_to_reparse = files_to_reparse or []
    previously_open_nodes = previously_open_nodes or set()

    # --- Bước 1: Xây dựng cấu trúc cây ảo hiệu quả ---
    tree_nodes = defaultdict(lambda: {'dirs': set(), 'files': set()})
    
    # Thêm tất cả các thư mục cha vào một set để xử lý một lần
    all_dirs = set()
    for path in all_file_paths:
        # Đảm bảo path được normalize và hợp lệ
        if not path or not path.strip():
            continue
        try:
            normalized_path = os.path.normpath(path)
            parent = os.path.dirname(normalized_path)
            while len(parent) >= len(root_path):
                all_dirs.add(parent)
                if parent == root_path: break
                parent = os.path.dirname(parent)
        except Exception:
            # Bỏ qua path không hợp lệ
            continue
    
    all_paths_in_tree = set(all_file_paths).union(all_dirs)

    # Gán file và thư mục vào cha của chúng
    for path in all_paths_in_tree:
        if path == root_path: continue
        try:
            parent = os.path.dirname(path)
            if path in all_dirs:
                tree_nodes[parent]['dirs'].add(path)
            else: # Đây là một file
                tree_nodes[parent]['files'].add(path)
        except Exception:
            # Bỏ qua path không hợp lệ
            continue

    # --- Bước 2: Lọc và xác định các mục cần hiển thị ---
    paths_to_show = set()
    is_filtered = bool(query or show_only_errors)
    
    if not is_filtered:
        paths_to_show = all_paths_in_tree
    else:
        failed_files_set = {path for path, info in file_info_cache.items() if info.get('error')}
        for path in all_file_paths:
            # Kiểm tra path hợp lệ
            if not path or not path.strip():
                continue
            try:
                # Kiểm tra điều kiện lọc
                if show_only_errors and path not in failed_files_set:
                    continue
                if query and query not in os.path.basename(path).lower() and query not in path.lower():
                    continue
                
                # Thêm path hiện tại và tất cả thư mục cha
                curr = path
                while len(curr) >= len(root_path):
                    paths_to_show.add(curr)
                    parent = os.path.dirname(curr)
                    if parent == curr: break
                    curr = parent
            except Exception:
                # Bỏ qua path không hợp lệ
                continue

    if not paths_to_show:
        return []

    # --- Bước 3: Tạo danh sách node cuối cùng cho Treeview ---
    final_nodes = []
    reparse_set = set(files_to_reparse)

    def build_nodes_recursively(parent_path):
        dirs = sorted(list(tree_nodes[parent_path]['dirs']))
        files = sorted(list(tree_nodes[parent_path]['files']))
        
        for path in dirs + files:
            if path not in paths_to_show:
                continue

            try:
                # Xác định chính xác một đường dẫn có phải là thư mục hay không
                is_dir = path in all_dirs
                item_name = os.path.basename(path)
                prefix, tags = "", []

                if not is_dir:
                    if file_info_cache.get(path, {}).get('error'):
                        tags.append("error")
                        prefix = "[Lỗi] "
                    elif path in reparse_set:
                        tags.append("changed" if path in file_info_cache else "new")
                        prefix = "[Thay đổi] " if path in file_info_cache else "[Mới] "
                    elif path in file_info_cache:
                        tags.append("cached")

                final_nodes.append({
                    'id': path,
                    'parent': parent_path if parent_path != root_path else '',
                    'text': f"{prefix}{item_name}",
                    'values': (path, 'dir' if is_dir else 'file'),
                    'tags': tuple(tags),
                    'open': is_filtered or path in previously_open_nodes
                })
                
                if is_dir:
                    build_nodes_recursively(path)
            except Exception:
                # Bỏ qua path không hợp lệ
                continue

    build_nodes_recursively(root_path)
    return final_nodes

def prepare_db_objects_tree_data(db_objects_data, query="") -> List[Dict[str, Any]]:
    """
    Chuẩn bị dữ liệu cây đối tượng DB.
    """
    nodes = []
    query = query.strip().lower()
    if not db_objects_data:
        return []

    for db_name in sorted(db_objects_data.keys()):
        objects = db_objects_data[db_name]
        
        objects_to_display = objects
        if query:
            filtered_objects = {}
            if objects:
                for obj_type, obj_list in objects.items():
                    if matched_list := [obj for obj in obj_list if query in obj.lower()]:
                        filtered_objects[obj_type] = matched_list
            if not filtered_objects:
                continue
            objects_to_display = filtered_objects

        db_node_id = f"db_{db_name}"
        nodes.append({'id': db_node_id, 'parent': '', 'text': db_name, 'open': bool(query)})

        if objects is None:
            nodes.append({'id': f"{db_node_id}_error", 'parent': db_node_id, 'text': "Lỗi kết nối hoặc không có quyền", 'tags': ('error',)})
            continue

        if not any(objects_to_display.values()):
            if not query:
                 nodes.append({'id': f"{db_node_id}_empty", 'parent': db_node_id, 'text': "Không có đối tượng nào", 'tags': ('faded',)})
            continue

        for obj_type, obj_list in sorted(objects_to_display.items()):
            if obj_list:
                type_node_id = f"{db_node_id}_{obj_type}"
                nodes.append({'id': type_node_id, 'parent': db_node_id, 'text': f"{obj_type} ({len(obj_list)})", 'open': bool(query)})
                for obj_name in sorted(obj_list):
                    nodes.append({'id': f"{type_node_id}_{obj_name}", 'parent': type_node_id, 'text': obj_name})
    return nodes

def prepare_usage_file_tree_data(file_paths, project_root_path="") -> List[Dict[str, Any]]:
    """
    Chuẩn bị dữ liệu cây thư mục từ một danh sách các đường dẫn file.
    """
    if not file_paths:
        return []

    nodes_dict = {}
    project_root_path = os.path.normpath(project_root_path) if project_root_path else ""

    for path in sorted(file_paths):
        current_path = os.path.normpath(path)
        try:
            display_path = os.path.relpath(current_path, project_root_path)
        except ValueError:
            display_path = current_path
            
        path_parts = display_path.split(os.sep)
        
        built_path = ""
        for i, part in enumerate(path_parts):
            parent_path = built_path
            built_path = os.path.join(parent_path, part) if parent_path else part
            
            is_dir = i < len(path_parts) - 1
            if built_path not in nodes_dict:
                nodes_dict[built_path] = {
                    'id': built_path,
                    'parent': parent_path,
                    'text': part,
                    'values': (current_path, 'dir' if is_dir else 'file'),
                    'open': True
                }
    return list(nodes_dict.values())
